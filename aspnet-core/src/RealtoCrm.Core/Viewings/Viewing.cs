namespace RealtoCrm.Viewings;

using System;
using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Clients;
using Comments;
using Employees;
using ExternalSearches;
using Matches;
using Nomenclatures;
using Offers;
using Projects;
using Searches;

public class Viewing : FullAuditedEntity<int>, IAddComment
{
    public DateTime StartDate { get; set; }

    public int StatusId { get; set; }

    public ViewingStatus Status { get; set; } = default!;

    public int MatchId { get; set; }

    public Match Match { get; set; } = default!;

    public int SearchId { get; set; }

    public Search Search { get; set; } = default!;

    public int SearchClientId { get; set; }

    public Client SearchClient { get; set; } = default!;

    public int? SearchEmployeeId { get; set; }

    public Employee? SearchEmployee { get; set; }

    public int OfferId { get; set; }

    public Offer Offer { get; set; } = default!;

    public int OfferClientId { get; set; }

    public Client OfferClient { get; set; } = default!;

    public int? OfferEmployeeId { get; set; }

    public Employee? OfferEmployee { get; set; }

    public int? ExternalOfferId { get; set; }

    public ExternalOffer? ExternalOffer { get; set; }

    public int? ExternalSearchId { get; set; }

    public ExternalSearch? ExternalSearch { get; set; }

    public int? ProjectId { get; set; }

    public Project? Project { get; set; }

    public ICollection<ViewingFile> ViewingsFiles { get; } = new List<ViewingFile>();

    public ICollection<ViewingComment> ViewingsComments { get; } = new List<ViewingComment>();

    public void AddComment(Comment comment)
        => this.ViewingsComments.Add(new ViewingComment
        {
            Comment = comment
        });
}