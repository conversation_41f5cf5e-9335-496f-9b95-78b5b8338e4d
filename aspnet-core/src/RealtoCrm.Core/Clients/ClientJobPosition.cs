namespace RealtoCrm.Clients;

using System;
using Abp.Domain.Entities.Auditing;
using RealtoCrm.Nomenclatures;

public class ClientJobPosition : IFullAudited
{
    public int ClientId { get; set; }

    public Client Client { get; set; }
    
    public int JobPositionId { get; set; }
    
    public JobPosition JobPosition { get; set; }
    
    public DateTime CreationTime { get; set; }
    
    public long? CreatorUserId { get; set; }
    
    public DateTime? LastModificationTime { get; set; }
    
    public long? LastModifierUserId { get; set; }
    
    public bool IsDeleted { get; set; }
    
    public DateTime? DeletionTime { get; set; }
    
    public long? DeleterUserId { get; set; }
}