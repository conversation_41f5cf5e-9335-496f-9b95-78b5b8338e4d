namespace RealtoCrm.Deposits;

using System;
using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Clients;
using Comments;
using Contracts;
using Employees;
using Matches;
using Money;
using Nomenclatures;
using Offers;
using Searches;

public class Deposit : FullAuditedEntity<int>, IAddComment
{
    public DateTime NotaryDate { get; set; }

    public DateTime SigningDate { get; set; }

    public DateTime SigningEndDate { get; set; }

    public Money Amount { get; set; } = default!;

    public Money OfferedPrice { get; set; } = default!;

    public int DepositStatusId { get; set; }

    public DepositStatus DepositStatus { get; set; } = default!;

    public int MatchId { get; set; }

    public Match Match { get; set; } = default!;

    public int SearchId { get; set; }

    public Search Search { get; set; } = default!;

    public int SearchClientId { get; set; }

    public Client SearchClient { get; set; } = default!;

    public int SearchEmployeeId { get; set; }

    public Employee SearchEmployee { get; set; } = default!;

    public int OfferId { get; set; }

    public Offer Offer { get; set; } = default!;

    public int OfferClientId { get; set; }

    public Client OfferClient { get; set; } = default!;

    public int OfferEmployeeId { get; set; }

    public Employee OfferEmployee { get; set; } = default!;

    public ICollection<Contract> Contracts { get; } = new List<Contract>();

    public ICollection<DepositFile> DepositsFiles { get; } = new List<DepositFile>();

    public ICollection<DepositComment> DepositsComments { get; } = new List<DepositComment>();

    public void AddComment(Comment comment)
        => this.DepositsComments.Add(new DepositComment
        {
            Comment = comment
        });
}