namespace RealtoCrm.Offers;

using System.Collections.Generic;
using Abp.Domain.Entities.Auditing;
using Deals;
using Nomenclatures;
using Viewings;

public class ExternalOffer : FullAuditedEntity<int>
{
    public string? Name { get; set; }

    public int? ExternalAgencyId { get; set; }

    public ExternalAgency? ExternalAgency { get; set; }

    public ICollection<Viewing> Viewings { get; } = new List<Viewing>();

    public ICollection<Deal> Deals { get; } = new List<Deal>();
}