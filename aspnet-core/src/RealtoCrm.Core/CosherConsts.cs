namespace RealtoCrm;

public static class CosherConsts
{
    public static class EstateCategories
    {
        public const string ResidentialPropertyEstateCategoryName = "Жилищен";

        public const string PlotAndLandEstateCategoryName = "Парцели и терени";

        public const string GarageEstateCategoryName = "Гараж";

        public const string BusinessPropertyEstateCategoryName = "Бизнес";

        public const string IndustrialPropertyEstateCategoryName = "Промишлен";

        public const string ApartmentEstateCategoryName = "Апартамент";
        public const string PlotOrLandEstateCategoryName = "Парцел/Терен";
        public const string HouseOrVillaEstateCategoryName = "Къща/Вила";
        public const string HouseFloorEstateCategoryName = "Етаж от къща";
        public const string AgriculturalLandEstateCategoryName = "Земеделска земя";
        public const string GarageOrParkingSlotEstateCategoryName = "Гараж/Паркомясто";
        public const string OfficeEstateCategoryName = "Офис";

        public const string StoreEstateCategoryName = "Магазин";

        public const string HotelOrMotelEstateCategoryName = "Хотел/Мотел";

        public const string OfficeBuildingOrShoppingCenterEstateCategoryName = "Офис Сграда/Търговски център";

        public const string RestaurantEstateCategoryName = "Заведение/Ресторант";

        public const string UnspecifiedEstateCategoryName = "Друго";

        public const string OutbuildingOrFarmEstateCategoryName = "Стопанска сграда/Ферма";
        public const string WorkshopOrWarehouseEstateCategoryName = "Цех/Склад";

        public const string ResidentialBuildingEstateCategoryName = "Жилищна сграда";
    }

    public static class EstateTypes
    {
        public const string OneBedroomEstateTypeName = "Едностаен";
        public const string TwoBedroomEstateTypeName = "Двустаен";
        public const string ThreeBedroomEstateTypeName = "Тристаен";
        public const string FourBedroomEstateTypeName = "Четиристаен";
        public const string MultiBedroomEstateTypeName = "Многостаен";
        public const string StudioEstateTypeName = "Гарсониера";
        public const string MaisonetteEstateTypeName = "Мезонет";

        public const string BoxonieraEstateTypeName = "Боксониера";

        public const string HouseFloorEstateTypeName = "Етаж от къща";
        public const string HouseOrVillaEstateTypeName = "Къща/Вила";

        public const string PlotOrLandEstateTypeName = "Парцел/Терен";
        public const string AgriculturalLandEstateTypeName = "Земеделска земя";

        public const string GarageEstateTypeName = "Гараж";
        public const string ParkingSlotEstateTypeName = "Паркомясто";
        public const string GarageCellarEstateTypeName = "Гаражна клетка";


        public const string OfficeEstateTypeName = "Офис";
        public const string StoreEstateTypeName = "Магазин";
        public const string PharmacyEstateTypeName = "Аптека";
        public const string MedicalOfficeEstateTypeName = "Медицински кабинет";
        public const string BeautyHairSalonEstateTypeName = "Козметично/Фризьорско студио";

        public const string HotelOrMotelEstateTypeName = "Хотел/Мотел";

        public const string OfficeBuildingOrShoppingCenterEstateTypeName = "Офис Сграда/Търговски център";

        public const string RestaurantEstateTypeName = "Заведение/Ресторант";

        public const string UnspecifiedEstateTypeName = "Друго";
        public const string IndustrialPropertyEstateTypeName = "Промишлен имот";
        public const string RetailPremisesEstateTypeName = "Търговски помещения";
        public const string FitnessEstateTypeName = "Фитнес";

        public const string OutbuildingOrFarmEstateTypeName = "Стопанска сграда/Ферма";
        public const string WorkshopOrWarehouseEstateTypeName = "Цех/Склад";
        public const string ResidentialBuildingEstateTypeName = "Жилищна сграда";
    }

    public static class SearchStatuses
    {
        public const string ActiveSearchStatusName = "Активно";
        public const string ArchivedSearchStatusName = "Архив";
        public const string DealSearchStatusName = "Сделка";
    }

    public static class Furnitures
    {
        public const string FullyFurnishedFurnitureName = "Пълно обзавеждане";
        public const string PartiallyFurnishedFurnitureName = "Частично обзавеждане";
        public const string NotFurnishedFurnitureName = "Без обзавеждане";
        public const string FurnishedToKeyFurnitureName = "До ключ";
        public const string FurnishedToPaintAndPlasterFurnitureName = "На шпакловка и замазка";
        public const string FullyEquippedFurnitureName = "Пълно оборудване";
        public const string PartiallyEquippedFurnitureName = "Частично оборудване";
        public const string NotEquippedFurnitureName = "Без оборудване";
    }

    public static class Conditions
    {
        public const string UnderConstructionConditionName = "В строеж";
        public const string NeedsRepairConditionName = "За ремонт";
        public const string GoodConditionName = "Добро";
        public const string ExcellentConditionName = "Отлично";
        public const string LuxConditionName = "Лукс";
    }

    public static class ContainerNames
    {
        public const string FilesContainerName = "files";
        public const string ViewingFilesContainerName = "viewing-files";
    }

    public static class CosherSourceCategoriesNames
    {
        public const string SphereOfInfluence = "Сфера на влияние";
        public const string PersonalSphere = "Лична сфера";
        public const string RecommendationSphere = "Препоръка сфера";
        public const string RecommendationFromOuterBroker = "Препоръка от външен брокер";
        public const string ColleagueRecommendation = "Препоръка от колега";
        public const string ConsultantActivity = "Консултантска дейност";
        public const string Archive = "Архив";
        public const string FreeOrPotentialClients = "Свободни потенциали/Клиенти";
        public const string PosterPrivateClientsInternet = "Обяви на частни лица - Интернет";
        public const string PosterPrivateClientsNewspaperMagazine = "Обяви на частни лица - Вестник/Списание";
        public const string FarmingDirectContact = "Фарминг директен контакт";
        public const string FarmingFlyersStickersBrochures = "Фарминг - флаери/стикери/брошури";
        public const string Banners = "Транспаранти";
        public const string VisitedOffice = "Посетил офис";
        public const string PersonalEmailing = "Личен е-майлинг";
        public const string SocialNetworks = "Социални мрежи";
        public const string OpenHouse = "Open House";
        public const string OtherConsultantActivity = "Друго /конс. дейност/";
        public const string PassedPotentialClients = "Подадени потенциали/клиенти";
        public const string PassedByCorporateClients = "Подадени от Корпоративни клиенти";
        public const string EmployeeInAddressImotekaAgCapital = "Служител в Адрес/Имотека/AG Capital";
        public const string Advertisement = "Реклама";
        public const string InternetAsking = "Интернет запитване";
        public const string EmailingTargetedEmailNewsLetter = "Е-мейлинг/Таргетиран имейл/Newsletter";
        public const string InternetCampaign = "Интернет кампания";
        public const string PrintAdvertisement = "Печатна реклама";
        public const string FlayerCampaignMagazine = "Флаер кампания/Списание";
        public const string GreenTelephone = "Зелен телефон";
        public const string Robobroker = "Робоброкер";
        public const string Other = "Друго";
    }

    public static class DealMotives
    {
        public const string LeavingInvestmentDealMotiveName = "Излиза от инвестицията";
        public const string SplittingDealMotiveName = "Разделяне на имота";
        public const string NeedMoneyDealMotiveName = "Необходимост от пари";
        public const string RepayingCreditDealMotiveName = "Погасяване на кредит";
        public const string SellingToBuyAnotherDealMotiveName = "Продава, за да купи друг имот";
        public const string ProductRealizationDealMotiveName = "Реализация на продукт";
        public const string ForInvestmentDealMotiveName = "За инвестиция";
        public const string ForBusinessDealMotiveName = "За бизнес";
        public const string LivingUnderRentDealMotiveName = "Живее под наем";
        public const string BuyingForLivingDealMotiveName = "Купува за живеене";
        public const string ChangingResidenceDealMotiveName = "Сменя жилището";
        public const string OtherDealMotiveName = "Друго";
    }

    public static class FacingDirections
    {
        public const string NorthFacingDirectionName = "Север";
        public const string NorthEastFacingDirectionName = "Североизток";
        public const string EastFacingDirectionName = "Изток";
        public const string SouthWestFacingDirectionName = "Югоизток";
        public const string SouthFacingDirectionName = "Юг";
        public const string SouthEastFacingDirectionName = "Югозапад";
        public const string WestFacingDirectionName = "Запад";
        public const string NorthWestFacingDirectionName = "Северозапад";
    }

    public static class CompletionLevels
    {
        public const string AfterConstructionAllowedCompletionLevelName = "След разрешение за строеж";
        public const string RoughConstructionCompletionLevelName = "Груб строеж";
        public const string Act15CompletionLevelName = "Акт 15";
        public const string Act16CompletionLevelName = "Акт 16";
    }

    public static class ConstructionTypes
    {
        public const string PanelConstructionTypeName = "Панел";
        public const string BrickConstructionTypeName = "Тухла";
        public const string ConcreteWithSteelConstructionTypeName = "ЕПК";
        public const string ConcreteConstructionTypeName = "ПК";
        public const string OtherConstructionTypeName = "Друго";
    }

    public static class Countries
    {
        public const string BulgariaName = "България";
        public const string SerbiaName = "Сърбия";
        public const string RussiaName = "Русия";
        public const string RomaniaName = "Румъния";
        public const string GreeceName = "Гърция";
        public const string LatviaName = "Латвия";
        public const string ItalyName = "Италия";

        public const string HungaryName = "Унгария";

        // public const string CreteName = "о-в Крит"; // Note: Crete is not a country, but an island part of Greece
        public const string EnglandName = "Англия";
        public const string CanadaName = "Канада";
        public const string USAName = "САЩ";
        public const string PolandName = "Полша";
        public const string SpainName = "Испания";
        public const string AustriaName = "Австрия";
        public const string GermanyName = "Германия";
        public const string TurkeyName = "Турция";
        public const string MacedoniaName = "Македония";
        public const string AustraliaName = "Австралия";

        public const string AzerbaijanName = "Азербайджан";

        // public const string AlandIslandsName = "Аландски острови"; // Note: Part of Finland
        public const string AlbaniaName = "Албания";
        public const string AlgeriaName = "Алжир";
        public const string AmericanSamoaName = "Американска Самоа";
        public const string USVirginIslandsName = "Американски Вирджински острови";
        public const string AngolaName = "Ангола";
        public const string AnguillaName = "Ангуила";
        public const string AndorraName = "Андора";
        public const string AntiguaAndBarbudaName = "Антигуа и Барбуда";
        public const string ArgentinaName = "Аржентина";
        public const string ArmeniaName = "Армения";
        public const string ArubaName = "Аруба";
        public const string AfghanistanName = "Афганистан";
        public const string BangladeshName = "Бангладеш";
        public const string BarbadosName = "Барбадос";
        public const string BahamasName = "Бахамски острови";
        public const string BahrainName = "Бахрейн";
        public const string BelarusName = "Беларус";
        public const string BelgiumName = "Белгия";
        public const string BelizeName = "Белиз";
        public const string BeninName = "Бенин";
        public const string BermudaName = "Бермудски острови";
        public const string BoliviaName = "Боливия";
        public const string BosniaAndHerzegovinaName = "Босна и Херцеговина";
        public const string BotswanaName = "Ботсвана";
        public const string BrazilName = "Бразилия";
        public const string BritishIndianOceanTerritoryName = "Британска територия в Индийския океан";
        public const string BritishVirginIslandsName = "Британски Вирджински острови";
        public const string BruneiName = "Бруней Даруссалам";
        public const string BouvetIslandName = "Буве";
        public const string BurkinaFasoName = "Буркина Фасо";
        public const string BurundiName = "Бурунди";
        public const string BhutanName = "Бутан";
        public const string VanuatuName = "Вануату";
        public const string VaticanName = "Ватикана";
        public const string VenezuelaName = "Венецуела";
        public const string VietnamName = "Виетнам";
        public const string GabonName = "Габон";
        public const string GambiaName = "Гамбия";
        public const string GhanaName = "Гана";
        public const string GuyanaName = "Гаяна";
        public const string GuadeloupeName = "Гваделупа";
        public const string GuatemalaName = "Гватемала";
        public const string GuineaName = "Гвинея";
        public const string GuineaBissauName = "Гвинея-Бисау";
        public const string GibraltarName = "Гибралтар";
        public const string GrenadaName = "Гренада";
        public const string GreenlandName = "Гренландия";
        public const string GeorgiaName = "Грузия";
        public const string GuamName = "Гуам";
        public const string GuernseyName = "Гърнси";
        public const string DenmarkName = "Дания";
        public const string DRCDName = "Демократична република Конго (Заир)";
        public const string DjiboutiName = "Джибути";
        public const string JerseyName = "Джърси";
        public const string DominicaName = "Доминика";
        public const string DominicanRepublicName = "Доминиканска република";
        public const string EgyptName = "Египет";
        public const string EcuadorName = "Еквадор";
        public const string EquatorialGuineaName = "Екваториална Гвинея";
        public const string ElSalvadorName = "Ел Салвадор";
        public const string EritreaName = "Еритрея";
        public const string EstoniaName = "Естония";
        public const string EthiopiaName = "Етиопия";
        public const string ZambiaName = "Замбия";
        public const string WesternSaharaName = "Западна Сахара";
        public const string ZimbabweName = "Зимбабве";
        public const string YemenName = "Йемен";
        public const string IsraelName = "Израел";
        public const string EastTimorName = "Източен Тимор";
        public const string IndiaName = "Индия";
        public const string IndonesiaName = "Индонезия";
        public const string JordanName = "Йордания";
        public const string IraqName = "Ирак";
        public const string IranName = "Иран";
        public const string IrelandName = "Ирландия";
        public const string IcelandName = "Исландия";
        public const string CapeVerdeName = "Кабо Верде (острови Зелени Нос)";
        public const string KazakhstanName = "Казахстан";
        public const string CaymanIslandsName = "Каймански острови";
        public const string CambodiaName = "Камбоджа";
        public const string CameroonName = "Камерун";
        public const string CaribbeanNetherlandsName = "Карибска Холандия";
        public const string QatarName = "Катар";
        public const string KenyaName = "Кения";
        public const string CyprusName = "Кипър";
        public const string KyrgyzstanName = "Киргизстан";
        public const string KiribatiName = "Кирибати";
        public const string ChinaName = "Китай";
        public const string CocosIslandsName = "Кокосови острови";
        public const string ChristmasIslandName = "Коледни острови";
        public const string ColombiaName = "Колумбия";
        public const string ComorosName = "Коморски острови";
        public const string CongoName = "Конго";
        public const string KosovoName = "Косово";
        public const string CostaRicaName = "Коста Рика";
        public const string CubaName = "Куба";
        public const string KuwaitName = "Кувейт";
        public const string CuracaoName = "Кюрасао";
        public const string LaosName = "Лаос";
        public const string LesothoName = "Лесото";
        public const string LiberiaName = "Либерия";
        public const string LibyaName = "Либия";
        public const string LebanonName = "Ливан";
        public const string LithuaniaName = "Литва";
        public const string LiechtensteinName = "Лихтенщайн";
        public const string LuxembourgName = "Люксембург";
        public const string MauritaniaName = "Мавритания";
        public const string MauritiusName = "Мавриций";
        public const string MadagascarName = "Мадагаскар";
        public const string MayotteName = "Майот";
        public const string MacaoName = "Макао";
        public const string MalawiName = "Малави";
        public const string MalaysiaName = "Малайзия";
        public const string MaldivesName = "Малдиви";
        public const string MaliName = "Мали";
        public const string MaltaName = "Малта";
        public const string IsleOfManName = "Ман (остров)";
        public const string MoroccoName = "Мароко";
        public const string MartiniqueName = "Мартиника";
        public const string MarshallIslandsName = "Маршалови острови";
        public const string MexicoName = "Мексико";
        public const string MyanmarName = "Мианмар";
        public const string MicronesiaName = "Микронезия";
        public const string MozambiqueName = "Мозамбик";
        public const string MoldovaName = "Молдова";
        public const string MonacoName = "Монако";
        public const string MongoliaName = "Монголия";
        public const string MontserratName = "Монсерат";
        public const string NamibiaName = "Намибия";
        public const string NauruName = "Науру";
        public const string NepalName = "Непал";
        public const string NigerName = "Нигер";
        public const string NigeriaName = "Нигерия";
        public const string NicaraguaName = "Никарагуа";
        public const string NiueName = "Ниуе";
        public const string NewZealandName = "Нова Зеландия";
        public const string NewCaledoniaName = "Нова Каледония";
        public const string NorwayName = "Норвегия";

        public const string NorfolkIslandName = "Норфолк (остров)";

        // public const string DubaiName = "Дубай"; // Note: Dubai is a city, not a country
        public const string OmanName = "Оман";
        public const string CookIslandsName = "Острови Кук";

        public const string UnitedArabEmirates = "Обединени Арабски Емирства";

        // public const string HeardAndMcDonaldIslandsName = "Острови Хърд и Макдоналд"; // Note: These are not populated and are Australian external territories
        public const string PakistanName = "Пакистан";
        public const string PalauName = "Палау";
        public const string PalestineName = "Палестина";
        public const string PanamaName = "Панама";
        public const string PapuaNewGuineaName = "Папуа Нова Гвинея";
        public const string ParaguayName = "Парагвай";
        public const string PeruName = "Перу";
        public const string PitcairnIslandsName = "Питкерн";
        public const string PortugalName = "Португалия";

        public const string PuertoRicoName = "Пуерто Рико";

        // public const string ReunionName = "Реюнион"; //In France
        public const string RwandaName = "Руанда";
        public const string SamoaName = "Самоа";
        public const string SanMarinoName = "Сан марино";
        public const string SaoTomeAndPrincipeName = "Сао Томе и Принсипи";
        public const string SaudiArabiaName = "Саудитска Арабия";
        public const string EswatiniName = "Свазиленд"; // Formerly known as Swaziland
        public const string SvalbardAndJanMayenName = "Свалбард и Ян Майен";
        public const string SaintHelenaName = "Света Елена (остров)";
        public const string NorthKoreaName = "Северна Корея";
        public const string NorthernMarianaIslandsName = "Северни Мариански острови";
        public const string SaintBarthelemyName = "Сейнт Бартс";
        public const string SaintVincentAndTheGrenadinesName = "Сейнт Винсент и Гренадини";
        public const string SaintKittsAndNevisName = "Сейнт Китс и Невис";
        public const string SaintLuciaName = "Сейнт Лусия";
        public const string SeychellesName = "Сейшели";
        public const string SaintMartinFrenchPartName = "Сен Мартен (Франция)";
        public const string SaintPierreAndMiquelonName = "Сен Пиер и Микелон";
        public const string SenegalName = "Сенегал";
        public const string SierraLeoneName = "Сиера Леоне";
        public const string SingaporeName = "Сингапур";
        public const string SintMaartenDutchPartName = "Синт Мартен (Холандия)";
        public const string SyriaName = "Сирия";
        public const string SlovakiaName = "Словакия";
        public const string SloveniaName = "Словения";
        public const string SolomonIslandsName = "Соломонови острови";
        public const string SomaliaName = "Сомалия";
        public const string SudanName = "Судан";
        public const string SurinameName = "Суринам";
        public const string TajikistanName = "Таджикистан";
        public const string TaiwanName = "Тайван";
        public const string ThailandName = "Тайланд";
        public const string TanzaniaName = "Танзания";

        public const string TogoName = "Того";

        // public const string TokelauName = "Токелау"; // Note: Tokelau is a dependent territory of New Zealand, not an independent country
        public const string TongaName = "Тонга";
        public const string TrinidadAndTobagoName = "Тринидад и Тобаго";
        public const string TuvaluName = "Тувалу";
        public const string TunisiaName = "Тунис";
        public const string TurkmenistanName = "Туркменистан";
        public const string TurksAndCaicosIslandsName = "Търкс и Кайкос";
        public const string UgandaName = "Уганда";
        public const string UzbekistanName = "Узбекистан";
        public const string UkraineName = "Украйна";
        public const string WallisAndFutunaName = "Уолис и Футуна";
        public const string UruguayName = "Уругвай";
        public const string FaroeIslandsName = "Фарьорски острови";
        public const string FijiName = "Фиджи";
        public const string PhilippinesName = "Филипини";
        public const string FinlandName = "Финландия";
        public const string FalklandIslandsName = "Фолкландски острови";
        public const string FranceName = "Франция";
        public const string FrenchPolynesiaName = "Френска Полинезия";
        public const string FrenchSouthernAndAntarcticLandsName = "Френски южни и антарктически територии";
        public const string FrenchGuianaName = "Френска Гвиана";
        public const string HaitiName = "Хаити";
        public const string NetherlandsName = "Холандия";
        public const string HondurasName = "Хондурас";
        public const string HongKongName = "Хонконг";
        public const string CroatiaName = "Хърватска";
        public const string CentralAfricanRepublicName = "Централноафриканска република";
        public const string ChadName = "Чад";
        public const string MontenegroName = "Черна гора";
        public const string CzechRepublicName = "Чехия";
        public const string ChileName = "Чили";
        public const string SwitzerlandName = "Швейцария";
        public const string SwedenName = "Швеция";
        public const string SriLankaName = "Шри Ланка";
        public const string SouthAfricaName = "ЮАР";
        public const string SouthSudanName = "Южен Судан";
        public const string SouthGeorgiaAndTheSouthSandwichIslandsName = "Южна Джорджия и Южни Сандвичеви острови";
        public const string SouthKoreaName = "Южна Корея";
        public const string JamaicaName = "Ямайка";
        public const string JapanName = "Япония";
        public const string ForeignCountry = "Чужбина";
    }

    public static class OperationTypes
    {
        public const string SellingOperationTypeName = "Продава";
        public const string RentingOperationTypeName = "Наема";
        public const string BuyingOperationTypeName = "Купува";
        public const string GivingOperationTypeName = "Отдава";
    }

    public static class ContractTypes
    {
        public const string CommissionContractTypeName = "Комисионен";
        public const string ExclusiveContractTypeName = "Ексклузивен";
        public const string CoexclusiveContractTypeName = "Коексклузивен";
        public const string SpecificEstateContractTypeName = "Договор за конкретен имот";
        public const string GuaranteeContractTypeName = "Договор за гаранция";
        public const string PreliminaryForPPContractTypeName = "Предварителен дог за П-П";
        public const string FinalContractTypeName = "Окончателен договор /Нотариален акт/";
        public const string RentContractTypeName = "Договор за наем";
    }

    public static class Vats
    {
        public const string WithoutVatName = "Без ДДС";
        public const string WithVatName = "С ДДС";
        public const string NotSubjectToVatName = "Не подлежи на ДДС";
    }

    public static class ProjectStatuses
    {
        public const string DraftProjectStatusName = "Чернова";
        public const string ActiveProjectStatusName = "Активен";
    }

    public static class DepositStatuses
    {
        public const string ActiveDepositStatusName = "Активен";
        public const string RejectedDepositStatusName = "Отказан/Върнат";
        public const string GivenToSellerDepositStatusName = "Предаден на продавача";
        public const string RedirectedDepositStatusName = "Пренасочен";
        public const string AnnexedDepositStatusName = "Анексиран";
        public const string UsedDepositStatusName = "Усвоен";
    }

    public static class OfferStatuses
    {
        public const string DraftOfferStatusName = "Чернова";
        public const string PotentialOfferStatusName = "Потенциал";
        public const string ActiveOfferStatusName = "Активна";
        public const string ArchiveOfferStatusName = "Архив";
        public const string DealOfferStatusName = "Сделка";
    }

    public static class Clients
    {
        public const int PersonalClientId = 1;
    }

    public static class Companies
    {
        public const string UniqueCompanyName = "Unique Estates";
    }

    public static class Divisions
    {
        public const string BulgariaDivisionName = "България";
    }

    public static class Offices
    {
        public const string OborishteOfficeName = "Оборище";
    }

    public static class ContactDetails
    {
        public const string PhoneContactDetailName = "Телефон";
        public const string EmailContactDetailName = "Имейл";
        public const string FacebookContactDetailName = "Facebook";
        public const string InstagramContactDetailName = "Instagram";
        public const string LinkedInContactDetailName = "LinkedIn";
        public const string WebsiteContactDetailName = "Website";
    }

    public static class ContactDetailTypes
    {
        public const string PhoneContactDetailTypeName = "Телефон";
        public const string EmailContactDetailTypeName = "Имейл";
        public const string LinkContactDetailTypeName = "Линк";
    }

    public static class Tenants
    {
        public const string AddressTenantName = "Address";
        public const string ImotekaTenantName = "Imoteka";
        public const string UniqueEstatesTenantName = "Unique Estates";
        public const string NewEstatesTenantName = "New Estates";
        public const string CWFortonTenantName = "CW Forton";
        public const string ImofondTenantName = "Imofond";
        public const string BOPartnersTenantName = "BOpartners";
        public const string RealtoTenantName = "Realto";
    }

    public static class TagCategories
    {
        public const string Interests = "Интереси";
        public const string Religion = "Религия";
        public const string Embassies = "Посолства";
    }

    public static class ClientPreferences
    {
        public const string NoSms = "Не желае СМС";
        public const string NoInvitations = "Не желае покани";
        public const string NoNewsletter = "Не желае бюлетин";
        public const string NoCalls = "Не желае да му се обаждаме";
        public const string DoNotDisturb = "Не желае да бъде обезпокояван";
    }

    public static class Nationalities
    {
        public const string Bulgarian = "Българска";
        public const string Foreign = "Чуждестранна";
    }

    public static class Titles
    {
        public const string Lawyer = "Адвокат";
        public const string Engineer = "Иженер";
        public const string Notary = "Нотариус";
        public const string Doctor = "Доктор";
        public const string Professor = "Професор";
        public const string Mister = "Господин";
        public const string Miss = "Госпожа";
        public const string Architect = "Архитект";
        public const string AssociateProfessor = "Доцент";
        public const string Ambassador = "Посланик";
        public const string Academic = "Академик";
    }

    public static class MaritalStatuses
    {
        public const string NotMarried = "Неженен";
    }

    public static class UesSourceCategories
    {
        public const string PropertyBrochureFoldersForms = "Брошура на имоти, папки, бланки";
        public const string Farming = "Фарминг";
        public const string InvestorSign = "Табела инвеститор";
        public const string NewConstructionUe = "Ново строителство - UE";
        public const string UesiteOnlineCall = "ЮЕ сайт онлайн обаждане";
        public const string UesiteCall = "ЮЕ сайт обаждане";
        public const string TargetedEmail = "Таргетиран мейл";
        public const string Banner = "Транспарант";
        public const string Campaign = "Кампания";
        public const string ZopimChat = "Zopim чат";
        public const string Platform = "Платформа";
        public const string OpenHouse = "Open House";
        public const string OfficeVisit = "Посетил офиса";
        public const string Other = "Други";
        public const string Event = "Събитие";
        public const string GoogleAds = "Реклама Google";
        public const string OffersBoard = "Табло оферти";
        public const string UniqueEstateLifeMagazine = "Unique Estate Life Magazine";
        public const string Recommendation = "Препоръка";
        public const string SphereOfInfluence = "Сфера на влияние";
        public const string SimeonovoAlleys = "Simeonovo Alleys";
        public const string Robobroker = "Робоброкер";
        public const string GoogleSearchCampaign = "Google search campaign";
        public const string SellPropertyWithUe = "Продайте имота с ЮЕ";
        public const string ColdClient = "Студен клиент";
        public const string SourceDetail = "Източник детайл";
        public const string LuxuryPropertyCatalog = "Луксозен каталог с имоти";
        public const string EstatePortals = "Портали за имоти (сайтове за имоти)";
        public const string ExplicitSourceDetail = "Explicit detail";
        public const string Letters = "Писма";
        public const string Brochures = "Флаери";
        public const string DoorHangers = "Door hangers";
        public const string UesSiteOnlineSurvey = "ЮЕ сайт онлайн запитване";
        public const string Newsletter = "Newsletter";
        public const string FacebookBot = "Facebook Бот";
        public const string BrokerEmail = "Писма на брокери";
        public const string MobileApp = "Mobile App";
    }

    public static class Websites
    {
        public const string Google = "google.com";
        public const string UesSite = "ues.bg";
        public const string AloBg = "Alo.bg";
        public const string HomesBg = "Homes.bg";
        public const string ChristieS = "Christie`s";
    }

    public static class SocialMedias
    {
        public const string LinkedIn = "LinkedIn";
        public const string Viber = "Viber";
        public const string Instagram = "Instagram";
        public const string Facebook = "Facebook";
    }

    public static class ImageCategories
    {
        public const string EstateImageCategoryName = "Снимка към имот";
        public const string SketchImageCategoryName = "Скица";
        public const string PersonImageCategoryName = "Снимка на човек";
        public const string LogoImageCategoryName = "Лого";
    }

    public static class FileCategories
    {
        public const string MarketingFileCategoryName = "Маркетинг";
        public const string BrochureFileCategoryName = "Брошура";
        public const string CommercialFileCategoryName = "Търговски";
        public const string SpaAnalysisFileCategoryName = "СПА анализи";
        public const string ExternalBrokerFileCategoryName = "Външен брокер";
        public const string JuridicalFileCategoryName = "Юридически";
        public const string OwnershipDocumentsFileCategoryName = "Документи за собственост";
        public const string EstateDocumentsFileCategoryName = "Документи за имот";
        public const string ContactDocumentsFileCategoryName = "Документи на контакт";
        public const string CompletionStageFileCategoryName = "Етап на завършване";
        public const string CreditFileCategoryName = "Кредитни";
        public const string DocumentPackageFileCategoryName = "Пакет Документи";
        public const string EncumbranceDocumentsFileCategoryName = "Документи за тежести";
        public const string OtherFileCategoryName = "Друго";
        public const string OthersFileCategoryName = "Други";
        public const string EnergyCertificateFileCategoryName = "Енергиен сертификат";
        public const string RegisterFileCategoryName = "Регистър";
    }

    public static class ClientTypes
    {
        public const string PersonalClientTypeName = "Физическо лице";
        public const string LegalClientTypeName = "Юридическо лице";
    }

    public static class HeatingSystems
    {
        public const string TEC = "ТЕЦ";
        public const string Gas = "Газ";
        public const string Electricity = "Електричество";
        public const string HeatPump = "Термопомпа";
        public const string LocalHeating = "Локално парно";
        public const string Pellets = "Пелети";
        public const string Other = "Друго";
    }

    public static class Garages
    {
        public const string GarageName = "Гараж";
        public const string DoubleGarage = "Двоен гараж";
        public const string ParkingSpot = "Паркомясто";
        public const string DoubleParkingSpot = "Двойно паркомясто";
    }

    public static class ArchiveReasons
    {
        public const string TemporaryNotLookingArchiveReasonName = "Временно спрял";
        public const string WrongNumberArchiveReasonName = "Грешен номер";
        public const string BoughtSoldThemselvesArchiveReasonName = "Продал/купил сам";
        public const string BoughtSoldWithCompetitionArchiveReasonName = "Продал/купил с конкурент";
        public const string AbandonedBuySellArchiveReasonName = "Отказал се от търсене/предлагане";
        public const string OtherArchiveReasonName = "Друго";
        public const string RentGaveThemselvesArchiveReasonName = "Отдал/наел сам";
        public const string RentGaveWithCompetitionArchiveReasonName = "Отдал/наел с конкурент";
        public const string ExclusiveContractWithCompetitionArchiveReasonName = "Ексклузивен договор с конкурент";
        public const string DuplicatedArchiveReasonName = "Дублирана";
        public const string ClientPassedAwayArchiveReasonName = "Починал клиент";
        public const string OfferSearchByCompetitionArchiveReasonName = "Оферта/търсене на конкурент";
        public const string TemporaryDeclinedDepositArchiveReasonName = "Отказан депозит (временна)";
    }

    public static class HouseTypes
    {
        public const string RowHouseName = "Редова къща";
        public const string DetachedHouseName = "Самостоятелна къща";
    }

    public static class LeaseTerms
    {
        public const string LongTermName = "Дългосрочен";
        public const string ShortTermName = "Краткосрочен";
    }

    public static class ViewingStatuses
    {
        public const string ProposedViewingStatusName = "Предложен";
        public const string PlannedViewingStatusName = "Планиран";
        public const string CompletedViewingStatusName = "Осъществен";
        public const string CancelledViewingStatusName = "Отменен";
        public const string RequestedByCustomerViewingStatusName = "Заявен от клиент";
    }

    public static class OfferImages
    {
        public const string OffersBlobContainerName = "offers";
        public const string OfferBlobFileName = "offer_{0}_{1}_{2}";
        public const string OfferBlobFileNameWithSize = "offer_{0}_{1}_{2}_{3}";
        public const string WatermarksBlobContainerName = "watermarks";
        public const string WatermarkBlobFileName = "{0}_watermark.png";
    }

    public static class ImageThumbnails
    {
        public const int NormalThumbnailMaxWidth = 1080;
        public const int MediumThumbnailMaxWidth = 640;
        public const int SmallThumbnailMaxWidth = 360;
    }

    public static class ContentTypes
    {
        public const string WebpContentType = "image/webp";
    }
}