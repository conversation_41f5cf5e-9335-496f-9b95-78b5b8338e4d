namespace RealtoCrm.Nomenclatures;

using System.Collections.Generic;
using EstateObjects;
using Estates;
using Searches;

public class Heating : Nomenclature<int>
{
    public ICollection<Estate> Estates { get; } = new List<Estate>();

    public ICollection<SearchHeating> SearchesHeating { get; } = new List<SearchHeating>();

    public ICollection<EstateGroupDetail> EstateGroupDetails { get; } = new List<EstateGroupDetail>();
}