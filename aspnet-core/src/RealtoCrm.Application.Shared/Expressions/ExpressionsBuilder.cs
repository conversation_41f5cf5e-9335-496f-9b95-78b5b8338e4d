namespace RealtoCrm.Expressions;

using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using Abp.Domain.Entities;
using Companies;
using DataCrudModels;

public class ExpressionsBuilder : IExpressionsBuilder
{
    public static Expression<Func<TEntity, bool>> BuildInRangeExpression<TEntity, TValue>(
        Expression<Func<TEntity, TValue>> propertySelector,
        TValue min,
        TValue max,
        bool inclusiveMin = true,
        bool inclusiveMax = false) where TValue : IComparable<TValue>
    {
        var parameter = Expression.Parameter(typeof(TEntity), "x");

        var property = Expression.Invoke(propertySelector, parameter);

        var minExpression = inclusiveMin
            ? Expression.GreaterThanOrEqual(property, Expression.Constant(min))
            : Expression.GreaterThan(property, Expression.Constant(min));

        var maxExpression = inclusiveMax
            ? Expression.LessThanOrEqual(property, Expression.Constant(max))
            : Expression.LessThan(property, Expression.Constant(max));

        var combinedExpression = Expression.AndAlso(minExpression, maxExpression);

        return Expression.Lambda<Func<TEntity, bool>>(combinedExpression, parameter);
    }

    public static Expression<Func<TEntity, bool>> BuildInRangeExpression<TEntity, TValue>(
        TValue targetValue,
        Expression<Func<TEntity, TValue>> minPropertySelector,
        Expression<Func<TEntity, TValue>> maxPropertySelector,
        bool inclusiveMin = true,
        bool inclusiveMax = false) where TValue : IComparable<TValue>
    {
        var parameter = Expression.Parameter(typeof(TEntity), "x");
    
        var minProperty = Expression.Invoke(minPropertySelector, parameter);
        var maxProperty = Expression.Invoke(maxPropertySelector, parameter);
    
        var targetConstant = Expression.Constant(targetValue);
    
        var minComparison = inclusiveMin
            ? Expression.GreaterThanOrEqual(targetConstant, minProperty)
            : Expression.GreaterThan(targetConstant, minProperty);
        
        var maxComparison = inclusiveMax
            ? Expression.LessThanOrEqual(targetConstant, maxProperty)
            : Expression.LessThan(targetConstant, maxProperty);
    
        var combinedExpression = Expression.AndAlso(minComparison, maxComparison);
    
        return Expression.Lambda<Func<TEntity, bool>>(combinedExpression, parameter);
    }

    public Expression<Func<TEntity, bool>> BuildDataCrudFiltersExpression<TEntity>(
        ParameterExpression parameter,
        IEnumerable<DataFilter>? filters,
        Dictionary<string, FilterExpression> customFilterExpressions)
    {
        var entityType = typeof(TEntity);

        var filterExpressions = filters
            ?.Select(dataFilter =>
            {
                var filterExpression = customFilterExpressions.GetValueOrDefault(dataFilter.NameFixed);

                return this.BuildExpressionForFilter(
                    dataFilter,
                    entityType,
                    parameter,
                    filterExpression?.PropertyInfo,
                    filterExpression?.PropertyExpression);
            })
            .Where(x => x != null)
            .ToList();

        if (filterExpressions == null || !filterExpressions.Any())
        {
            return x => true;
        }

        var isAnd = true;
        var expression = isAnd
            ? filterExpressions.Aggregate(Expression.AndAlso)
            : filterExpressions.Aggregate(Expression.OrElse);

        return expression == null
            ? (Expression<Func<TEntity, bool>>)(x => true)
            : Expression.Lambda<Func<TEntity, bool>>(expression, parameter);
    }

    public IEnumerable<SortByExpressionDefinition<TEntity>> BuildDataCrudSortByExpression<TEntity, TPrimaryKey>(
        IEnumerable<DataSorter> sortBys)
        where TEntity : Entity<TPrimaryKey>
    {
        var entityType = typeof(TEntity);
        var parameter = Expression.Parameter(entityType, "x");

        return sortBys?.Any() == true
            ? sortBys.Select(sortBy => BuildExpressionForSortBy<TEntity>(parameter, sortBy))
            : [SortByExpressionDefinition<TEntity>.Ascending<TEntity>(x => x.Id)];
    }

    public SortByExpressionDefinition<TEntity> BuildDistanceSortByExpression<TEntity, TValue>(
        Expression<Func<TEntity, TValue?>> valueSelector,
        TValue? referenceValue)
        where TValue : struct, IComparable<TValue>
    {
        var distanceExpression = BuildDistanceExpression(valueSelector, referenceValue);

        var parameter = Expression.Parameter(typeof(TEntity), "x");
        var invokedDistance = Expression.Invoke(distanceExpression, parameter);
        var convertedToObject = Expression.Convert(invokedDistance, typeof(object));

        return new SortByExpressionDefinition<TEntity>
        {
            Expression = Expression.Lambda<Func<TEntity, object>>(convertedToObject, parameter),
            Direction = DataSortDirection.Ascending
        };
    }

    public FilterExpression BuildForCompanyName<TEntity>(ParameterExpression parameter)
        => new(
            typeof(Company).GetProperty(nameof(Company.Name))!,
            Expression.PropertyOrField(
                Expression.PropertyOrField(parameter, "Company"), nameof(Company.Name)));

    private static Expression<Func<TEntity, TValue>> BuildDistanceExpression<TEntity, TValue>(
        Expression<Func<TEntity, TValue?>> valueSelector,
        TValue? referenceValue)
        where TValue : struct, IComparable<TValue>
    {
        var parameter = Expression.Parameter(typeof(TEntity), "x");

        var propertyAccess = Expression.Invoke(valueSelector, parameter);

        var propertyHasValue = Expression.Property(propertyAccess, "HasValue");
        var propertyValue = Expression.Property(propertyAccess, "Value");
        var referenceHasValue = Expression.Constant(referenceValue.HasValue);
        var referenceValueParam = Expression.Constant(referenceValue ?? default(TValue));

        var bothNonNull = Expression.AndAlso(propertyHasValue, referenceHasValue);
        var bothNull = Expression.AndAlso(
            Expression.Not(propertyHasValue),
            Expression.Not(referenceHasValue));

        var actualDifference = Expression.Subtract(propertyValue, referenceValueParam);
        var absoluteDifference = Expression.Call(
            typeof(Math),
            nameof(Math.Abs),
            null,
            actualDifference);

        var maxValue = Expression.Constant(typeof(TValue) == typeof(double)
            ? (TValue)(object)double.MaxValue
            : (TValue)(object)int.MaxValue);

        var result = Expression.Condition(
            bothNonNull,
            absoluteDifference,
            Expression.Condition(
                bothNull,
                Expression.Constant(default(TValue)),
                maxValue
            ));

        return Expression.Lambda<Func<TEntity, TValue>>(result, parameter);
    }

    public static Expression<Func<TEntity, bool>> BuildExpressionBeforeDate<TEntity>(int months)
    {
        var parameter = Expression.Parameter(typeof(TEntity), "x");

        var creationTimeProperty = Expression.Property(parameter, "CreationTime");

        var sixMonthsAgo = Expression.Constant(DateTime.Now.AddMonths(-months));

        var comparison = Expression.LessThan(creationTimeProperty, sixMonthsAgo);

        return Expression.Lambda<Func<TEntity, bool>>(comparison, parameter);
    }

    private static SortByExpressionDefinition<TEntity> BuildExpressionForSortBy<TEntity>(ParameterExpression parameter, DataSorter sorter)
    {
        // x.Name
        var member = Expression.PropertyOrField(parameter, sorter.Name);
        // (object)x.Name
        var conversion = Expression.Convert(member, typeof(object));

        // x => (object)x.Name
        // x => (object)x.PopulatedPlaceId
        return new SortByExpressionDefinition<TEntity>
        {
            Expression = Expression.Lambda<Func<TEntity, object>>(conversion, parameter),
            Direction = sorter.Direction
        };
    }

    private Expression? BuildExpressionForFilter(
        DataFilter filter,
        Type entityType,
        ParameterExpression parameter,
        PropertyInfo? customPropertyInfo = null,
        Expression? customProperty = null)
    {
        var propertyInfo = customPropertyInfo ?? entityType.GetProperty(filter.NameFixed);

        if (string.IsNullOrEmpty(filter.Name)
            || string.IsNullOrEmpty(filter.Value)
            || filter.Operator == null
            || propertyInfo == null)
        {
            return null;
        }

        // estateType.{Property}
        var propertyValue = customProperty ?? Expression.PropertyOrField(parameter, filter.NameFixed);

        // filter.Value
        Expression targetValue = Expression.Constant(filter.Value);

        if (propertyInfo.PropertyType == typeof(string))
        {
            propertyValue = Expression.Call(propertyValue, nameof(string.ToLower), null);
            targetValue = Expression.Call(targetValue, nameof(string.ToLower), null);
        }

        if (propertyInfo.PropertyType == typeof(bool))
        {
            targetValue = Expression.Constant(bool.Parse(filter.Value));
        }

        if (propertyInfo.PropertyType == typeof(int))
        {
            targetValue = Expression.Constant(int.Parse(filter.Value));
        }

        if (propertyInfo.PropertyType == typeof(long))
        {
            targetValue = Expression.Constant(long.Parse(filter.Value));
        }

        if (propertyInfo.PropertyType == typeof(double))
        {
            targetValue = Expression.Constant(double.Parse(filter.Value));
        }

        if (propertyInfo.PropertyType == typeof(DateTime))
        {
            var isValidDate = DateTime.TryParse(filter.Value, out var dateTime);

            if (!isValidDate)
            {
                dateTime = DateTime.ParseExact(filter.Value, "dd/MM/yyyy", CultureInfo.InvariantCulture);
            }

            targetValue = Expression.Constant(dateTime);
        }

        if (propertyInfo.PropertyType.IsEnum)
        {
            propertyValue = Expression.Convert(propertyValue, typeof(int));
            targetValue = Expression.Constant(int.Parse(filter.Value));
        }

        var typeFilters = this.GetFiltersByType(propertyInfo.PropertyType, propertyValue, targetValue);

        var (_, expression) = typeFilters.FirstOrDefault(x => x.Item1 == filter.Operator);

        return expression;
    }

    private IEnumerable<(DataFilterOperator, Expression)> GetFiltersByType(Type type, Expression propertyValue, Expression targetValue)
        => type == typeof(string)
            ? this.GetStringFilters(propertyValue, targetValue)
            : type == typeof(bool)
                ? this.GetBooleanFilters(propertyValue, targetValue)
                : type == typeof(int) || type == typeof(long) || type == typeof(double) || type.IsEnum
                    ? this.GetIntegerFilters(propertyValue, targetValue)
                    : type == typeof(DateTime)
                        ? this.GetDateTimeFilters(propertyValue, targetValue)
                        : [];

    private IEnumerable<(DataFilterOperator, Expression)> GetStringFilters(Expression propertyValue, Expression targetValue)
        =>
        [
            // estateType.{Property} == filter.Value
            (DataFilterOperator.Equals, Expression.Equal(propertyValue, targetValue)),

            // estateType.{Property} != filter.Value
            (DataFilterOperator.NotEquals, Expression.NotEqual(propertyValue, targetValue)),

            // estateType.{Property}.Contains(filter.Value)
            (DataFilterOperator.Contains, Expression.Call(propertyValue, nameof(string.Contains), null, targetValue)),

            // !estateType.{Property}.Contains(filter.Value)
            (DataFilterOperator.NotContains, Expression.Not(Expression.Call(propertyValue, nameof(string.Contains), null, targetValue))),

            // estateType.{Property}.StarsWith(filter.Value)
            (DataFilterOperator.StartsWith, Expression.Call(propertyValue, nameof(string.StartsWith), null, targetValue)),

            // estateType.{Property}.EndsWith(filter.Value)
            (DataFilterOperator.EndsWith, Expression.Call(propertyValue, nameof(string.EndsWith), null, targetValue)),
        ];

    private IEnumerable<(DataFilterOperator, Expression)> GetBooleanFilters(Expression propertyValue, Expression targetValue)
        =>
        [
            (DataFilterOperator.Equals, Expression.Equal(propertyValue, targetValue)),
            (DataFilterOperator.NotEquals, Expression.NotEqual(propertyValue, targetValue)),
        ];

    private IEnumerable<(DataFilterOperator, Expression)> GetIntegerFilters(Expression propertyValue, Expression targetValue)
        =>
        [
            (DataFilterOperator.Equals, Expression.Equal(propertyValue, targetValue)),
            (DataFilterOperator.NotEquals, Expression.NotEqual(propertyValue, targetValue)),
            (DataFilterOperator.Gt, Expression.GreaterThan(propertyValue, targetValue)),
            (DataFilterOperator.Gte, Expression.GreaterThanOrEqual(propertyValue, targetValue)),
            (DataFilterOperator.Lt, Expression.LessThan(propertyValue, targetValue)),
            (DataFilterOperator.Lte, Expression.LessThanOrEqual(propertyValue, targetValue)),
        ];

    private IEnumerable<(DataFilterOperator, Expression)> GetDateTimeFilters(Expression propertyValue, Expression targetValue)
        =>
        [
            // x.{Property} >= filter.Value && x.{Property} < filter.Value.AddDays(1)
            (
                DataFilterOperator.DateIs,
                Expression.And(
                    Expression.GreaterThanOrEqual(propertyValue, targetValue),
                    Expression.LessThan(propertyValue, BuildAddDaysMethodCallExpression(targetValue)))),

            // !(x.{Property} >= filter.Value && x.{Property} < filter.Value.AddDays(1))
            (
                DataFilterOperator.DateIsNot,
                Expression.Not(
                    Expression.And(
                        Expression.GreaterThanOrEqual(propertyValue, targetValue),
                        Expression.LessThan(propertyValue, BuildAddDaysMethodCallExpression(targetValue))))),

            // x.{Property} < filter.Value
            (DataFilterOperator.DateBefore, Expression.LessThan(propertyValue, targetValue)),

            // x.{Property} > filter.Value.AddDays(1)
            (DataFilterOperator.DateAfter, Expression.GreaterThan(
                propertyValue,
                BuildAddDaysMethodCallExpression(targetValue))),
        ];

    private static MethodCallExpression BuildAddDaysMethodCallExpression(Expression targetValue, double days = 1d)
        => Expression.Call(targetValue, nameof(DateTime.AddDays), null, Expression.Constant(days));
}