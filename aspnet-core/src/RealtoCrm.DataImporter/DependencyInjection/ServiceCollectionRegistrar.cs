namespace RealtoCrm.DataImporter.DependencyInjection;

using Abp.Dependency;
using Castle.Windsor.MsDependencyInjection;
using Identity;
using Microsoft.Extensions.DependencyInjection;
using Services;
using Services.Implementation;
using Services.Implementation.Offers;

public static class ServiceCollectionRegistrar
{
    public static void Register(this IIocManager iocManager)
    {
        var services = new ServiceCollection();
        services.AddIdentity();
        services
            .AddTransient<EmployeesAsiImporterService, EmployeesAsiImporterService>()
            .AddTransient<EmployeesUesImporterService, EmployeesUesImporterService>()
            .AddTransient<EmployeesFortonImporterService, EmployeesFortonImporterService>()
            .AddTransient<ProvinceMappingsUesImporterService, ProvinceMappingsUesImporterService>()
            .AddTransient(typeof(IEmployeesImporterService<>), typeof(EmployeesImporterService<>))
            .AddTransient<ClientsAsiImporterService, ClientsAsiImporterService>()
            .AddTransient<ClientsUesImporterService, ClientsUesImporterService>()
            .AddTransient<SectorMappingAsiImporterService, SectorMappingAsiImporterService>()
            .AddTransient<SectorMappingUesImporterService, SectorMappingUesImporterService>()
            .AddTransient<RelatedClientsAsiImporterService, RelatedClientsAsiImporterService>()
            .AddTransient<RelatedClientsUesImporterService, RelatedClientsUesImporterService>()
            .AddTransient<ClientSourceCategoriesUesImporterService, ClientSourceCategoriesUesImporterService>()
            .AddTransient<JobPositionsAsiImporterService, JobPositionsAsiImporterService>()
            .AddTransient<DivisionsAsiImporterService, DivisionsAsiImporterService>()
            .AddTransient<ProvinceMappingsAsiImporterService, ProvinceMappingsAsiImporterService>()
            .AddTransient<MunicipalitiesAsiImporterService, MunicipalitiesAsiImporterService>()
            .AddTransient<MunicipalitiesUesImporterService, MunicipalitiesUesImporterService>()
            .AddTransient<DivisionsSecondAsiImporterService, DivisionsSecondAsiImporterService>()
            .AddTransient<DivisionsUesImporterService, DivisionsUesImporterService>()
            .AddTransient<DivisionsFortonImporterService, DivisionsFortonImporterService>()
            .AddTransient<FranchiseDivisionsAsiImporterService, FranchiseDivisionsAsiImporterService>()
            .AddTransient<TeamsAsiImporterService, TeamsAsiImporterService>()
            .AddTransient<TeamsFortonImporterService, TeamsFortonImporterService>()
            .AddTransient<WorkplacesAsiImporterService, WorkplacesAsiImporterService>()
            .AddTransient<WorkplacesUesImporterService, WorkplacesUesImporterService>()
            .AddTransient<PopulatedPlacesAsiImporterService, PopulatedPlacesAsiImporterService>()
            .AddTransient<PopulatedPlacesUesImporterService, PopulatedPlacesUesImporterService>()
            .AddTransient<DistrictsAsiImporterService, DistrictsAsiImporterService>()
            .AddTransient<DistrictsUesImporterService, DistrictsUesImporterService>()
            .AddTransient<StreetsAsiImporterService, StreetsAsiImporterService>()
            .AddTransient<StreetsUesImporterService, StreetsUesImporterService>()
            .AddTransient<OfficesAsiImporterService, OfficesAsiImporterService>()
            .AddTransient<OfficesUesImporterService, OfficesUesImporterService>()
            .AddTransient<OfficesFortonImporterService, OfficesFortonImporterService>()
            .AddTransient<ExternalAgenciesAsiImporterService, ExternalAgenciesAsiImporterService>()
            .AddTransient<ExternalAgenciesUesImporterService, ExternalAgenciesUesImporterService>()
            .AddTransient<IImportDocumentFetchService, ImportDocumentFetchService>()
            .AddTransient<DepartmentsAsiImporterService, DepartmentsAsiImporterService>()
            .AddTransient<DepartmentsFortonImporterService, DepartmentsFortonImporterService>()
            .AddTransient<OffersAsiImporterService, OffersAsiImporterService>()
            .AddTransient<OffersUesImporterService, OffersUesImporterService>()
            .AddTransient<OfferImageAsiImporterService, OfferImageAsiImporterService>()
            .AddTransient<OfferImageUesImporterService, OfferImageUesImporterService>()
            .AddTransient<SearchesAsiImporterService, SearchesAsiImporterService>()
            .AddTransient<SearchesUesImporterService, SearchesUesImporterService>()
            .AddTransient<DepartmentsUesImporterService, DepartmentsUesImporterService>()
            .AddTransient<TeamsUesImporterService, TeamsUesImporterService>()
            .AddTransient<OfficeNewEstateImporterService, OfficeNewEstateImporterService>()
            .AddTransient<DivisionsNewEstateImporterService, DivisionsNewEstateImporterService>()
            .AddTransient<TeamsNewEstatesImporterService, TeamsNewEstatesImporterService>()
            .AddTransient<DepartmentsNewEstatesImporterService, DepartmentsNewEstatesImporterService>()
            .AddTransient<EmployeesNewEstatesImporterService, EmployeesNewEstatesImporterService>()
            .AddTransient<EmployeeUesAdditionsImporterService, EmployeeUesAdditionsImporterService>()
            .AddTransient<DepartmentsUesAdditionsImporterService, DepartmentsUesAdditionsImporterService>()
            .AddTransient<TeamsUesAdditionsImporterService, TeamsUesAdditionsImporterService>()
            .AddTransient<AdministrativeDivisionTranslationsImporterService, AdministrativeDivisionTranslationsImporterService>()
            .AddTransient(typeof(IJsonSerializerService<>), typeof(JsonSerializerService<>))
            .AddTransient(typeof(IAddressMappingService), typeof(AddressMappingService))
            .AddTransient(typeof(IMapMoneyService), typeof(MapMoneyService))
            .AddTransient(typeof(IMapSourceCategoryService), typeof(MapSourceCategoryService));

        WindsorRegistrationHelper.CreateServiceProvider(iocManager.IocContainer, services);
    }
}