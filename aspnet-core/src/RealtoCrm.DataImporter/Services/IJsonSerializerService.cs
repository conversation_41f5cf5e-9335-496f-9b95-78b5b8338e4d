namespace RealtoCrm.DataImporter.Services;

using System.Collections.Generic;
using System.Threading.Tasks;
using Abp.Dependency;

public interface IJsonSerializerService<T> : ITransientDependency
{
    Task<IEnumerable<T>?> DeserializeManyAsync(string json);
    
    Task<IEnumerable<T>?> DeserializePageAsync(string json);

    Task<IEnumerable<T>?> DeserializeImageAsync(string json);
    
    Task<T?> DeserializeOneAsync(string json);
}