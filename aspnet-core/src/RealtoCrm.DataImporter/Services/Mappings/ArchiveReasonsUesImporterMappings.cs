namespace RealtoCrm.DataImporter.Services.Mappings;

using System.Collections.Generic;
using static CosherConsts.ArchiveReasons;

public class ArchiveReasonsUesImporterMappings
{
    public static readonly int[] ArchiveReasonsExceptionsMapToStatusDeal
        = [12, 23, 24, 25, 28, 29];

    public static readonly int[] SearchesArchiveReasonsExceptionsMapToStatusDeal
        = [12, 24];

    public static readonly Dictionary<int, string> ArchiveReasonUesIdToCosherName = new()
    {
        { 1, OtherArchiveReasonName}, // не иска да подписва договор
        { 2, OtherArchiveReasonName }, // няма контакт с клиента
        { 3, OtherArchiveReasonName }, // живее в чужбина
        { 4, TemporaryNotLookingArchiveReasonName },
        { 5, TemporaryNotLookingArchiveReasonName },
        { 6, OtherArchiveReasonName }, // не желая да работя с него
        { 7, OtherArchiveReasonName }, // търсене с нисък бюджет
        { 8, OtherArchiveReasonName }, // не желая да работя с него
        { 9, OtherArchiveReasonName },  // не желая да работя с него
        { 10, BoughtSoldThemselvesArchiveReasonName },
        { 11, BoughtSoldWithCompetitionArchiveReasonName },
        { 13, OtherArchiveReasonName }, // не иска да работи с нас
        { 14, OtherArchiveReasonName }, // няма качен договор
        { 15, WrongNumberArchiveReasonName },
        { 16, AbandonedBuySellArchiveReasonName },
        { 17, RentGaveWithCompetitionArchiveReasonName },
        { 18, RentGaveThemselvesArchiveReasonName },
        { 19, BoughtSoldThemselvesArchiveReasonName },
        { 20, BoughtSoldWithCompetitionArchiveReasonName },
        { 21, RentGaveWithCompetitionArchiveReasonName },
        { 22, RentGaveThemselvesArchiveReasonName },
        { 26, OtherArchiveReasonName}, // стар договор ?
        { 27, ExclusiveContractWithCompetitionArchiveReasonName },
        { 30, OtherArchiveReasonName }, // Конфиденциална оферта
        { 31, OtherArchiveReasonName }, // Оставен депозит
        { 32, DuplicatedArchiveReasonName },
        { 33, ClientPassedAwayArchiveReasonName },
        { 34, OtherArchiveReasonName }, // пред продажба с unique estates
        { 35, OtherArchiveReasonName }, // пред отдаване с unique estates
        { 36, OtherArchiveReasonName }, // Оглед с друга агенция
        { 37, OtherArchiveReasonName }, // Оглед с друга агенция на английски
        { 38, DuplicatedArchiveReasonName }
    };

    public static readonly Dictionary<int, string> SearchArchiveReasonUesIdToCosherName = new()
    {
        { 1, OtherArchiveReasonName}, // не иска да подписва договор
        { 2, OtherArchiveReasonName }, // няма контакт с клиента
        { 3, OtherArchiveReasonName }, // живее в чужбина
        { 4, TemporaryNotLookingArchiveReasonName },
        { 5, TemporaryNotLookingArchiveReasonName },
        { 6, OtherArchiveReasonName }, 
        { 7, OtherArchiveReasonName },
        { 8, OtherArchiveReasonName },
        { 9, OtherArchiveReasonName },
        { 10, BoughtSoldThemselvesArchiveReasonName },
        { 11, BoughtSoldWithCompetitionArchiveReasonName },
        { 13, OtherArchiveReasonName },
        { 14, OtherArchiveReasonName },
        { 15, WrongNumberArchiveReasonName },
        { 16, BoughtSoldThemselvesArchiveReasonName },
        { 17, BoughtSoldWithCompetitionArchiveReasonName },
        { 18, AbandonedBuySellArchiveReasonName },
        { 19, RentGaveThemselvesArchiveReasonName },
        { 20, RentGaveWithCompetitionArchiveReasonName },
        { 21, ExclusiveContractWithCompetitionArchiveReasonName },
        { 22, OfferSearchByCompetitionArchiveReasonName },
        { 23, OtherArchiveReasonName },
        { 25, OtherArchiveReasonName },
        { 26, OtherArchiveReasonName},
        { 27, TemporaryDeclinedDepositArchiveReasonName },
        { 28, OtherArchiveReasonName},
        { 29, OtherArchiveReasonName},
        { 31, OtherArchiveReasonName },
        { 32, DuplicatedArchiveReasonName },
        { 33, ClientPassedAwayArchiveReasonName },
        { 34, OtherArchiveReasonName }, // пред продажба с unique estates
        { 35, OtherArchiveReasonName }, // пред отдаване с unique estates
        { 36, OtherArchiveReasonName }, // Оглед с друга агенция
        { 37, OtherArchiveReasonName }, // Оглед с друга агенция на английски
        { 38, DuplicatedArchiveReasonName }
    };
}