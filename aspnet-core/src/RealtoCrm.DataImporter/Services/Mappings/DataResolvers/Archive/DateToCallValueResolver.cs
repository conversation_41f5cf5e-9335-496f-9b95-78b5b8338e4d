namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Archive;

using System;
using AutoMapper;
using Models;
using RealtoCrm.Offers;
using RealtoCrm.Searches;

public class DateToCallValueResolver : IValueResolver<OfferUesImportModel, Offer, DateTime?>,
    IValueResolver<SearchUesImportModel, Search, DateTime?>
{
    public DateTime? Resolve(OfferUesImportModel source, Offer destination, DateTime? destMember,
        ResolutionContext context)
    {
        if (source.ArchiveReasonUesId is null or 0 || string.IsNullOrWhiteSpace(source.DateToCall) ||
            !ArchiveReasonsUesImporterMappings.ArchiveReasonUesIdToCosherName
                .ContainsKey((int)source.ArchiveReasonUesId))
        {
            return null;
        }

        return !DateTime.TryParse(source.DateToCall, out var validDate)
            ? null
            : validDate;
    }

    public DateTime? Resolve(SearchUesImportModel source, Search destination, DateTime? destMember, ResolutionContext context)
    {
        if (source.ArchiveReasonUesId is null or 0 || string.IsNullOrWhiteSpace(source.DateToCall) ||
            !ArchiveReasonsUesImporterMappings.SearchArchiveReasonUesIdToCosherName
                .ContainsKey((int)source.ArchiveReasonUesId))
        {
            return null;
        }

        return !DateTime.TryParse(source.DateToCall, out var validDate)
            ? null
            : validDate;
    }
}