namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Offers;
using static RealtoCrm.CosherConsts.OperationTypes;

public class OperationTypeValueResolver
    : IValueResolver<OfferUesImportModel, Offer, OperationType>,
        IValueResolver<OfferAsiImportModel, Offer, OperationType>
{
    public const string OperationTypesItemsKey = nameof(OperationTypeValueResolver);

    private const string DefaultOperationName = SellingOperationTypeName;

    private static Dictionary<int, string> UesOperationTypeIdToOperationTypeName =>
        new()
        {
            { 1, SellingOperationTypeName },
            { 2, GivingOperationTypeName },
        };

    private static Dictionary<int, string> AsiOperationTypeIdToOperationTypeName =>
        new()
        {
            { 1, SellingOperationTypeName },
            { 2, GivingOperationTypeName },
        };

    public OperationType Resolve(OfferUesImportModel source, Offer destination, OperationType destMember, ResolutionContext context)
        => GetOperationType(source.OperationId, context.Items[OperationTypesItemsKey] as List<OperationType>, UesOperationTypeIdToOperationTypeName);

    public OperationType Resolve(OfferAsiImportModel source, Offer destination, OperationType destMember, ResolutionContext context)
        => GetOperationType(source.OperationId, context.Items[OperationTypesItemsKey] as List<OperationType>, AsiOperationTypeIdToOperationTypeName);

    private static OperationType? GetOperationType(int? operationId, List<OperationType> operationTypes, Dictionary<int, string> operationMappings)
    {
        var defaultValue = operationTypes.FirstOrDefault(x => x.Name == DefaultOperationName);
        return operationId.HasValue && operationMappings.ContainsKey(operationId.Value)
            ? operationTypes.FirstOrDefault(x => x.Name == operationMappings[operationId.Value]) ?? defaultValue
            : defaultValue;
    }
}