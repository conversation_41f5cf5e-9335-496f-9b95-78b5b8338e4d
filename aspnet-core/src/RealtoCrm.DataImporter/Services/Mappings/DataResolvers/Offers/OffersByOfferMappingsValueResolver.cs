using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Offers;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;

public class OffersByOfferMappingsValueResolver : IValueResolver<ViewingsUesImportModel, Viewing, int>
{
    
    public const string OffersByOfferMappingsItemsKey = nameof(OffersByOfferMappingsValueResolver);

    public int Resolve(ViewingsUesImportModel source, Viewing destination, int destMember, ResolutionContext context)
    {
        var offersByOfferMappings = context.Items[OffersByOfferMappingsItemsKey] as List<OfferMapping>;

        var offerMapping = offersByOfferMappings.FirstOrDefault(om => om.AdminId == source.OfferId);
        return offerMapping != null ? offerMapping!.OfferId : 0;
    }
}