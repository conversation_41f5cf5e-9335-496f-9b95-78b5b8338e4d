namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Models;
using RealtoCrm.Clients;
using RealtoCrm.Offers;
using RealtoCrm.Searches;

public class ClientValueResolver : IValueResolver<OfferUesImportModel, Offer, Client?>,
    IValueResolver<SearchUesImportModel, Search, Client?>
{
    public const string ClientsMappingsItemsKey = nameof(ClientValueResolver);

    public Client? Resolve(OfferUesImportModel source, Offer destination, Client? destMember, ResolutionContext context)
    {
        var clientMappings = context.Items[ClientsMappingsItemsKey] as List<ClientMapping>;
        var client = clientMappings.FirstOrDefault(x => source.Estate?.Clients.Count > 0 &&
                                                        x.AdminId == source.Estate.Clients[0]
                                                            .ClientId);

        return client is { ClientId: > 0 }
            ? client.Client
            : null;
    }

    public Client? Resolve(SearchUesImportModel source, Search destination, Client? destMember,
        ResolutionContext context)
    {
        var clientMappings = context.Items[ClientsMappingsItemsKey] as List<ClientMapping>;
        var client = clientMappings.FirstOrDefault(x =>
            source.ClientId != null && x.AdminId == source.ClientId);

        return client is { ClientId: > 0 }
            ? client.Client
            : null;
    }
}