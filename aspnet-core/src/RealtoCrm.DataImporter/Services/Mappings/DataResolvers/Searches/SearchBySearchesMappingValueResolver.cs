using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Searches;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Searches;

public class SearchBySearchesMappingValueResolver : IValueResolver<ViewingsUesImportModel, Viewing, int>
{
    public const string SearchesItemsKey = "SearchItemsKey";

    public const string SearchesMappingsItemsKey = nameof(SearchBySearchesMappingValueResolver);

    public int Resolve(ViewingsUesImportModel source, Viewing destination, int destMember, ResolutionContext context)
    {
        if(source.Match?.ClientSearchId is null)
        {
            return 0;
        }
        
        var searchesMappings = context.Items[SearchesMappingsItemsKey] as List<SearchMapping>;

        var searchMapping = searchesMappings.FirstOrDefault(x => x.AdminId == source.Match.ClientSearchId);

        return searchMapping is null ? 0 : searchMapping.SearchId;
    }
}