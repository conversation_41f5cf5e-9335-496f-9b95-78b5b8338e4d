using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Matches;
using RealtoCrm.Matches.Models;
using RealtoCrm.Offers;
using RealtoCrm.Searches;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;

public class MatchByViewingValueResolver : IValueResolver<ViewingsUesImportModel, Viewing, Match>
{
    public const string MatchByViewingValueResolverItemsKey = nameof(MatchByViewingValueResolver);

    public Match Resolve(ViewingsUesImportModel source, Viewing destination, Match destMember, ResolutionContext context)
    {
        if (source.Match is null)
        {
            return new Match();
        }
        
        var offerMappings = context.Items[OffersByOfferMappingsValueResolver.OffersByOfferMappingsItemsKey] as List<OfferMapping>;
        var searchesMappings = context.Items[SearchClientIdBySearchMappingValueResolver.SearchesMappingItemsKey] as List<SearchMapping>;
        
        var offerMapping = offerMappings.FirstOrDefault(om => om.AdminId == source.OfferId);
        var searchMapping = searchesMappings.FirstOrDefault(sm => sm.AdminId == source.Match.ClientSearchId);

        if(offerMapping == null || searchMapping == null)
        {
            return new Match();
        }
        
        var isDateInFuture = DateTime.Parse(source.Date) > DateTime.UtcNow;

        return new Match
        {
            OfferId = offerMapping.OfferId,
            SearchId = searchMapping.SearchId,
            StatusId = isDateInFuture
                ? (int)MatchStatusId.ForViewing
                : (int)MatchStatusId.Viewed,
            MatchMapping = new MatchMapping
            {
                AdminId = source.Match.id
            }
        };
    }
}