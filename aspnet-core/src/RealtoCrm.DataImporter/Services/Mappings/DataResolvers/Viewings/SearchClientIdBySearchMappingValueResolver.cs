using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Searches;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;

public class SearchClientIdBySearchMappingValueResolver : IValueResolver<ViewingsUesImportModel, Viewing, int>
{
    public const string SearchesMappingItemsKey = nameof(SearchClientIdBySearchMappingValueResolver);
    public const string SearchesItemsKey = "ViewingSearchesItemsKey";

    public int Resolve(ViewingsUesImportModel source, Viewing destination, int destMember, ResolutionContext context)
    {
        if(source.Match?.ClientSearchId is null)
        {
            return 0;
        }
        
        var searchesMappings = context.Items[SearchesMappingItemsKey] as List<SearchMapping>;
        var searches = context.Items[SearchesItemsKey] as List<Search>;

        var searchId = searchesMappings.FirstOrDefault(x => x.AdminId == source.Match.ClientSearchId)?.SearchId;
        if (searchId == null)
        {
            return 0;
        }

        var clientId = searches.FirstOrDefault(x => x.Id == searchId)?.ClientId;
        
        return clientId ?? 0;
    }
}