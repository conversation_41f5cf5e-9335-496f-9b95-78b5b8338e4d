using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Searches;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;

public class SearchEmployeeBySearchMappings : IValueResolver<ViewingsUesImportModel, Viewing, int?>
{
    public const string SearchEmployeeBySearchMappingsItemsKey = nameof(SearchEmployeeBySearchMappings);

    public int? Resolve(ViewingsUesImportModel source, Viewing destination, int? destMember, ResolutionContext context)
    {

        if(source.Match?.ClientSearchId is null)
        {
            return null;
        }
        
        var searchesMappings = context.Items[SearchEmployeeBySearchMappingsItemsKey] as List<SearchMapping>;
        var searchMapping = searchesMappings.FirstOrDefault(x => x.AdminId == source.Match.ClientSearchId);
        if (searchMapping == null)
        {
            return null;
        }

        return searchMapping.Search?.EmployeeId ?? null;
    }
}