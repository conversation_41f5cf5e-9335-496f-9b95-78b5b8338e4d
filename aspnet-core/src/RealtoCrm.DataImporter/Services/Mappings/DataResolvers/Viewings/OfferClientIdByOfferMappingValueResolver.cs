using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Offers;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;

public class OfferClientIdByOfferMappingValueResolver : IValueResolver<ViewingsUesImportModel, Viewing, int>
{
    
    public const string OffersByOfferMappingsItemsKey = nameof(OfferClientIdByOfferMappingValueResolver);
    public const string OffersItemsKey = "ViewingOffersItemsKey";

    
    public int Resolve(ViewingsUesImportModel source, Viewing destination, int destMember, ResolutionContext context)
    {
        var offerMappings = context.Items[OffersByOfferMappingsItemsKey] as List<OfferMapping>;
        var offerMapping = offerMappings.FirstOrDefault(om => om.AdminId == source.OfferId);
        
        if (offerMapping == null)
        {
            return 0;
        }
        

        return offerMapping.Offer?.ClientId ?? 0;
    }
}