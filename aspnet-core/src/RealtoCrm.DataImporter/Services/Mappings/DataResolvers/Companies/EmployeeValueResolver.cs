namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Companies;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Employees;
using RealtoCrm.Offers;
using RealtoCrm.Searches;

public class EmployeeValueResolver : IValueResolver<OfferUesImportModel, Offer, Employee?>,
    IValueResolver<SearchUesImportModel, Search, Employee?>
{
    public const string EmployeeMappingsItemsKey = nameof(EmployeeValueResolver);

    public static readonly Employee NonExistentEmployee = new()
    {
        Id = -1,
    };

    public Employee? Resolve(OfferUesImportModel source, Offer destination, Employee? destMember,
        ResolutionContext context)
    {
        if (source?.UserId == null)
        {
            return NonExistentEmployee;
        }

        var employeeMappings = context.Items[EmployeeMappingsItemsKey] as List<EmployeeMapping>;
        var correspondingEmployee = employeeMappings.FirstOrDefault(x
            => source.UserId != null && x.AdminId == source.UserId);

        var isValidEmployee = correspondingEmployee is { EmployeeId: > 0 };

        return isValidEmployee
            ? correspondingEmployee?.Employee
            : NonExistentEmployee;
    }

    public Employee? Resolve(SearchUesImportModel source, Search destination, Employee? destMember,
        ResolutionContext context)
    {
        if (source?.UserId == null)
        {
            return null;
        }

        var employeeMappings = context.Items[EmployeeMappingsItemsKey] as List<EmployeeMapping>;
        var correspondingEmployee = employeeMappings.FirstOrDefault(x
            => source.UserId != null && x.AdminId == source.UserId);

        var isValidEmployee = correspondingEmployee is { EmployeeId: > 0 };

        return isValidEmployee
            ? correspondingEmployee?.Employee
            : null;
    }
}