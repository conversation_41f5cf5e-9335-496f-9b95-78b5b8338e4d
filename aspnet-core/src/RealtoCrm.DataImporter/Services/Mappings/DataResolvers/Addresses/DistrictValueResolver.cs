namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Addresses;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Extensions;
using RealtoCrm.Addresses;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures;
using RealtoCrm.Searches;

public class DistrictValueResolver : IValueResolver<EstateForOfferUesImportModel, Address, District?>,
    IValueResolver<SearchUesImportModel, Search, ICollection<SearchDistrict>>
{
    public const string DistrictMappingsItemsKey = nameof(DistrictValueResolver);
    private static readonly Dictionary<int, int> QuarterExceptionMappings = new()
    {
        { 1346, 793 },  // Quarter ID -> District ID
        { 1694, 1036 }  // Quarter ID -> District ID
    };

    public District? Resolve(EstateForOfferUesImportModel source, Address destination, District? destMember,
        ResolutionContext context)
    {
        if (source.QuarterId == null)
        {
            return null;
        }

        var districtMappings = context.Items[DistrictMappingsItemsKey] as List<DistrictMapping>;
        var districtMapping = districtMappings.FirstOrDefault(p =>
            p.AdminID == source.QuarterId
            || p.AsiId == source.QuarterId);
        return districtMapping?.District;
    }

    public ICollection<SearchDistrict> Resolve(SearchUesImportModel source, Search destination,
        ICollection<SearchDistrict> destMember, ResolutionContext context)
    {
        var result = new List<SearchDistrict?>();
        var districtMappings = context.Items[DistrictMappingsItemsKey] as List<DistrictMapping>;

        if (source.Quarters == null || !source.Quarters.Any())
        {
            return result;
        }

        source.Quarters.ForEach(asiQuarter =>
        {
            if (QuarterExceptionMappings.TryGetValue(asiQuarter.Id, out int mappedDistrictId) && 
                !result.Any(d => d.DistrictId == mappedDistrictId))
            {
                result.Add(new SearchDistrict { DistrictId = mappedDistrictId });
                return;
            }

            var matchingDistrict = districtMappings.FirstOrDefault(d => d.AdminID == asiQuarter.Id);
            
            if (matchingDistrict != null && !result.Any(d => d.DistrictId == matchingDistrict.DistrictId))
            {
                result.Add(new SearchDistrict
                {
                    DistrictId = matchingDistrict.DistrictId
                });
            }
        });

        return result;
    }
}