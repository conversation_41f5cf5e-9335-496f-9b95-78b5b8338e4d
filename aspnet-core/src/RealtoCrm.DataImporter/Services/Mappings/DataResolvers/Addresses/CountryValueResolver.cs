namespace RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Addresses;

using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using RealtoCrm.Addresses;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures;
using static RealtoCrm.CosherConsts.Countries;

public class CountryValueResolver
        : IValueResolver<EstateForOfferUesImportModel, Address, Country?>,
        IValueResolver<OfferAsiEstate, Address, Country?>, 
        IValueResolver<ProvinceUesImportModel, Province, Country>
{
    public const string CountriesItemsKey = nameof(CountryValueResolver);

    private Dictionary<int, string> UesCountryIdsToCountryNames = new()
    {
        { 1, BulgariaName }, // България
        { 2, SerbiaName }, // Сърбия
        { 3, RussiaName }, // Русия
        { 4, RomaniaName }, // Румъния
        { 5, GreeceName }, // Гърция
        { 6, LatviaName }, // Латвия
        { 7, ItalyName }, // Италия
        { 8, HungaryName }, // Унгария
        { 9, GreeceName }, // о-в Крит (Not a country constant)
        { 10, EnglandName }, // Англия
        { 11, CanadaName }, // Канада
        { 12, USAName }, // САЩ
        { 13, PolandName }, // Полша
        { 14, SpainName }, // Испания
        { 15, AustriaName }, // Австрия
        { 16, GermanyName }, // Германия
        { 17, TurkeyName }, // Турция
        { 18, MacedoniaName }, // Македония
        { 20, AustraliaName }, // Австралия
        { 21, AzerbaijanName }, // Азербайджан
        { 22, FinlandName }, // Оландски острови (Not found in constants)
        { 23, AlbaniaName }, // Албания
        { 24, AlgeriaName }, // Алжир
        { 25, AmericanSamoaName }, // Американска Самоа
        { 26, USVirginIslandsName }, // Американски Вирджински острови
        { 27, AngolaName }, // Ангола
        { 28, AnguillaName }, // Ангуила
        { 29, AndorraName }, // Андора
        // { 30, "Антарктика" }, // Антарктика (Not found in constants)
        { 31, AntiguaAndBarbudaName }, // Антигуа и Барбуда
        { 32, ArgentinaName }, // Аржентина
        { 33, ArmeniaName }, // Армения
        { 34, ArubaName }, // Аруба
        { 35, AfghanistanName }, // Афганистан
        { 36, BangladeshName }, // Бангладеш
        { 37, BarbadosName }, // Барбадос
        { 38, BahamasName }, // Бахамски острови
        { 39, BahrainName }, // Бахрейн
        { 40, BelarusName }, // Беларус
        { 41, BelgiumName }, // Белгия
        { 42, BelizeName }, // Белиз
        { 43, BeninName }, // Бенин
        { 44, BermudaName }, // Бермудски острови
        { 45, BoliviaName }, // Боливия
        { 46, BosniaAndHerzegovinaName }, // Босна и Херцеговина
        { 47, BotswanaName }, // Ботсвана
        { 48, BrazilName }, // Бразилия
        { 49, BritishIndianOceanTerritoryName }, // Британска територия в Индийския океан
        { 50, BritishVirginIslandsName }, // Британски Вирджински острови
        { 51, BruneiName }, // Бруней Даруссалам
        { 53, BouvetIslandName }, // Буве
        { 54, BurkinaFasoName }, // Буркина Фасо
        { 55, BurundiName }, // Бурунди
        { 56, BhutanName }, // Бутан
        { 57, VanuatuName }, // Вануату
        { 58, VaticanName }, // Ватикана
        { 59, VenezuelaName }, // Венецуела
        { 60, VietnamName }, // Виетнам
        { 61, GabonName }, // Габон
        { 62, GambiaName }, // Гамбия
        { 63, GhanaName }, // Гана
        { 64, GuyanaName }, // Гаяна
        { 65, GuadeloupeName }, // Гваделупа
        { 66, GuatemalaName }, // Гватемала
        { 67, GuineaName }, // Гвинея
        { 68, GuineaBissauName }, // Гвинея-Бисау
        { 69, GibraltarName }, // Гибралтар
        { 70, GrenadaName }, // Гренада
        { 71, GreenlandName }, // Гренландия
        { 72, GeorgiaName }, // Грузия
        { 73, GuamName }, // Гуам
        { 74, GuernseyName }, // Гърнси
        { 75, DenmarkName }, // Дания
        { 76, DRCDName }, // Демократична република Конго (Заир)
        { 77, DjiboutiName }, // Джибути
        { 78, JerseyName }, // Джърси
        { 79, DominicaName }, // Доминика
        { 80, DominicanRepublicName }, // Доминиканска република
        { 81, EgyptName }, // Египет
        { 82, EcuadorName }, // Еквадор
        { 83, EquatorialGuineaName }, // Екваториална Гвинея
        { 84, ElSalvadorName }, // Ел Салвадор
        { 85, EritreaName }, // Еритрея
        { 86, EstoniaName }, // Естония
        { 87, EthiopiaName }, // Етиопия
        { 88, ZambiaName }, // Замбия
        { 89, WesternSaharaName }, // Западна Сахара
        { 90, ZimbabweName }, // Зимбабве
        { 91, YemenName }, // Йемен
        { 92, IsraelName }, // Израел
        { 93, EastTimorName }, // Източен Тимор
        { 94, IndiaName }, // Индия
        { 95, IndonesiaName }, // Индонезия
        { 96, JordanName }, // Йордания
        { 97, IraqName }, // Ирак
        { 98, IranName }, // Иран
        { 99, IrelandName }, // Ирландия
        { 100, IcelandName }, // Исландия
        { 101, CapeVerdeName }, // Кабо Верде (острови Зелени Нос)
        { 102, KazakhstanName }, // Казахстан
        { 103, CaymanIslandsName }, // Каймански острови
        { 104, CambodiaName }, // Камбоджа
        { 105, CameroonName }, // Камерун
        { 106, CaribbeanNetherlandsName }, // Карибска Холандия
        { 107, QatarName }, // Катар
        { 108, KenyaName }, // Кения
        { 109, CyprusName }, // Кипър
        { 110, KyrgyzstanName }, // Киргизстан
        { 111, KiribatiName }, // Кирибати
        { 112, ChinaName }, // Китай
        { 113, CocosIslandsName }, // Кокосови острови
        { 114, ChristmasIslandName }, // Коледни острови
        { 115, ColombiaName }, // Колумбия
        { 116, ComorosName }, // Коморски острови
        { 117, CongoName }, // Конго
        { 118, CostaRicaName }, // Коста Рика
        { 119, CubaName }, // Куба
        { 120, KuwaitName }, // Кувейт
        { 121, CuracaoName }, // Кюрасао
        { 122, LaosName }, // Лаос
        { 123, LesothoName }, // Лесото
        { 124, LiberiaName }, // Либерия
        { 125, LibyaName }, // Либия
        { 126, LebanonName }, // Ливан
        { 127, LithuaniaName }, // Литва
        { 128, LiechtensteinName }, // Лихтенщайн
        { 129, LuxembourgName }, // Люксембург
        { 130, MauritaniaName }, // Мавритания
        { 131, MauritiusName }, // Мавриций
        { 132, MadagascarName }, // Мадагаскар
        { 133, MayotteName }, // Майот
        { 134, MacaoName }, // Макао
        { 135, MalawiName }, // Малави
        { 136, MalaysiaName }, // Малайзия
        { 137, MaldivesName }, // Малдиви
        { 138, MaliName }, // Мали
        { 139, MaltaName }, // Малта
        { 140, IsleOfManName }, // Ман (остров)
        { 141, MoroccoName }, // Мароко
        { 142, MartiniqueName }, // Мартиника
        { 143, MarshallIslandsName }, // Маршалови острови
        { 144, MexicoName }, // Мексико
        { 145, MyanmarName }, // Мианмар
        { 146, MicronesiaName }, // Микронезия
        { 147, MozambiqueName }, // Мозамбик
        { 148, MoldovaName }, // Молдова
        { 149, MonacoName }, // Монако
        { 150, MongoliaName }, // Монголия
        { 151, MontserratName }, // Монсерат
        { 152, NamibiaName }, // Намибия
        { 153, NauruName }, // Науру
        { 154, NepalName }, // Непал
        { 155, NigerName }, // Нигер
        { 156, NigeriaName }, // Нигерия
        { 157, NicaraguaName }, // Никарагуа
        { 158, NiueName }, // Ниуе
        { 159, NewZealandName }, // Нова Зеландия
        { 160, NewCaledoniaName }, // Нова Каледония
        { 161, NorwayName }, // Норвегия
        { 162, NorfolkIslandName }, // Норфолк (остров)
        { 163, UnitedArabEmirates }, // Дубай (Not a country constant)
        { 164, OmanName }, // Оман
        { 165, CookIslandsName }, // Острови Кук
        { 166, AustraliaName }, // Острови Хърд и Макдоналд (Not in constants)
        { 167, PakistanName }, // Пакистан
        { 168, PalauName }, // Палау
        { 169, PalestineName }, // Палестина
        { 170, PanamaName }, // Панама
        { 171, PapuaNewGuineaName }, // Папуа Нова Гвинея
        { 172, ParaguayName }, // Парагвай
        { 173, PeruName }, // Перу
        { 174, PitcairnIslandsName }, // Питкерн
        { 175, PortugalName }, // Португалия
        { 176, PuertoRicoName }, // Пуерто Рико
        { 177, "Реюнион" }, // Реюнион (Not in constants)
        { 178, RwandaName }, // Руанда
        { 179, SamoaName }, // Самоа
        { 180, SanMarinoName }, // Сан марино
        { 181, SaoTomeAndPrincipeName }, // Сао Томе и Принсипи
        { 182, SaudiArabiaName }, // Саудитска Арабия
        { 183, EswatiniName }, // Свазиленд
        { 184, SvalbardAndJanMayenName }, // Свалбард и Ян Майен
        { 185, SaintHelenaName }, // Света Елена (остров)
        { 186, NorthKoreaName }, // Северна Корея
        { 187, NorthernMarianaIslandsName }, // Северни Мариански острови
        { 188, SaintBarthelemyName }, // Сейнт Бартс
        { 189, SaintVincentAndTheGrenadinesName }, // Сейнт Винсент и Гренадини
        { 190, SaintKittsAndNevisName }, // Сейнт Китс и Невис
        { 191, SaintLuciaName }, // Сейнт Лусия
        { 192, SeychellesName }, // Сейшели
        { 193, SaintMartinFrenchPartName }, // Сен Мартен (Франция)
        { 194, SaintPierreAndMiquelonName }, // Сен Пиер и Микелон
        { 195, SenegalName }, // Сенегал
        { 196, SierraLeoneName }, // Сиера Леоне
        { 197, SingaporeName }, // Сингапур
        { 198, SintMaartenDutchPartName }, // Синт Мартен (Холандия)
        { 199, SyriaName }, // Сирия
        { 200, SlovakiaName }, // Словакия
        { 201, SloveniaName }, // Словения
        { 202, SolomonIslandsName }, // Соломонови острови
        { 203, SomaliaName }, // Сомалия
        { 204, SudanName }, // Судан
        { 205, SurinameName }, // Суринам
        { 206, TajikistanName }, // Таджикистан
        { 207, TaiwanName }, // Тайван
        { 208, ThailandName }, // Тайланд
        { 209, TanzaniaName }, // Танзания
        { 210, TogoName }, // Того
        { 211, NewZealandName }, // Токелау (Not in constants)
        { 212, TongaName }, // Тонга
        { 213, TrinidadAndTobagoName }, // Тринидад и Тобаго
        { 214, TuvaluName }, // Тувалу
        { 215, TunisiaName }, // Тунис
        { 216, TurkmenistanName }, // Туркменистан
        { 217, TurksAndCaicosIslandsName }, // Търкс и Кайкос
        { 218, UgandaName }, // Уганда
        { 219, UzbekistanName }, // Узбекистан
        { 220, UkraineName }, // Украйна
        { 221, WallisAndFutunaName }, // Уолис и Футуна
        { 222, UruguayName }, // Уругвай
        { 223, FaroeIslandsName }, // Фарьорски острови
        { 224, FijiName }, // Фиджи
        { 225, PhilippinesName }, // Филипини
        { 226, FinlandName }, // Финландия
        { 227, FalklandIslandsName }, // Фолкландски острови
        { 228, FranceName }, // Франция
        { 229, FrenchPolynesiaName }, // Френска Полинезия
        { 230, FrenchSouthernAndAntarcticLandsName }, // Френски южни и антарктически територии
        { 231, FrenchGuianaName }, // Френска Гвиана (In constants as FrenchGuianaName)
        { 232, HaitiName }, // Хаити
        { 233, NetherlandsName }, // Холандия
        { 234, HondurasName }, // Хондурас
        { 235, HongKongName }, // Хонконг
        { 236, CroatiaName }, // Хърватска
        { 237, CentralAfricanRepublicName }, // Централноафриканска република
        { 238, ChadName }, // Чад
        { 239, MontenegroName }, // Черна гора
        { 240, CzechRepublicName }, // Чехия
        { 241, ChileName }, // Чили
        { 242, SwitzerlandName }, // Швейцария
        { 243, SwedenName }, // Швеция
        { 244, SriLankaName }, // Шри Ланка
        { 245, SouthAfricaName }, // ЮАР
        { 246, SouthSudanName }, // Южен Судан
        { 247, SouthGeorgiaAndTheSouthSandwichIslandsName }, // Южна Джорджия и Южни Сандвичеви острови
        { 248, SouthKoreaName }, // Южна Корея
        { 249, JamaicaName }, // Ямайка
        { 250, JapanName }, // Япония
        { 251, ItalyName }, // Италия (Duplicate of 7)
        { 252, MauritiusName }, // Мавриций (Duplicate of 131)
        { 253, KosovoName }, // Косово
        { 257, FranceName }, // Франция (Duplicate of 228)
        { 258, CanadaName }, // Канада (Duplicate of 11)
        { 260, IndonesiaName }, // Бали, Индонезия (Not in constants)
        { 261, IndonesiaName }, // о. Бали (Not in constants)
        { 262, SaintBarthelemyName } // Сейнт Бартс (Duplicate of 188)
    };

    private Dictionary<int, string> AsiCountryIdsToCountryNames = new()
    {
        { 1, SerbiaName },
        { 2, BulgariaName },
        { 3, RussiaName },
    };

    public Country? Resolve(EstateForOfferUesImportModel source, Address destination, Country? destMember, ResolutionContext context)
        => GetCountry(
            source.CountryId,
            context.Items[CountriesItemsKey] as List<Country>,
            this.UesCountryIdsToCountryNames);
    
    
    public Country? Resolve(ProvinceUesImportModel source, Province destination, Country? destMember, ResolutionContext context)
        => GetCountry(
            source.CountryId,
            context.Items[CountriesItemsKey] as List<Country>,
            this.UesCountryIdsToCountryNames);
    
    public Country? Resolve(OfferAsiEstate source, Address destination, Country? destMember, ResolutionContext context)
        => GetCountry(
            source.CountryId,
            context.Items[CountriesItemsKey] as List<Country>,
            this.AsiCountryIdsToCountryNames);

    public static Country? GetCountry(int? countryId, List<Country> countries, Dictionary<int, string> countriesMappings)
    {
        var defaultCountry = countries.FirstOrDefault(x => x.Name == BulgariaName);

        var country = countryId switch
        {
            null => defaultCountry,
            var _ when countriesMappings.ContainsKey(countryId.Value) =>
                countries.FirstOrDefault(x => x.Name == countriesMappings[countryId.Value]),
            var _ => defaultCountry,
        };

        return country ?? defaultCountry;
    }
}