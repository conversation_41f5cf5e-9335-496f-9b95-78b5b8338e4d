namespace RealtoCrm.DataImporter.Services.Implementation.Offers;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Clients;
using Companies;
using Employees;
using Estates;
using Mappings.DataResolvers;
using Mappings.DataResolvers.Addresses;
using Mappings.DataResolvers.Archive;
using Mappings.DataResolvers.Companies;
using Mappings.DataResolvers.Estates;
using Mappings.DataResolvers.Offers;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Nomenclatures;
using RealtoCrm.Offers;
using SourceCategories;

public class OffersUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<OfferUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Offer, OfferUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/offers";

    protected override IEnumerable<Offer> ApplySpecifics(IEnumerable<Offer> entities)
    {
        var entitiesList = entities.ToList();
        var invalidId = 0;
        var totalEntitiesCount = entitiesList.Count;

        var allPopulatedPlaces = this.DbContext.PopulatedPlaces
            .Select(pp => pp.Id)
            .ToList();

        var countries = this.DbContext.Countries
            .Select(c => c.Id)
            .ToList();

        var entitiesToSave = entitiesList
            .Where(x => x.Client != null)
            .Where(x => x.Estate.Address.Country != null && countries.Contains(x.Estate.Address.Country.Id))
            .Where(x => x.Estate.Address.PopulatedPlace != null && allPopulatedPlaces.Contains(x.Estate.Address.PopulatedPlace.Id))
            .Where(x => x.Estate.Type != null)
            .Where(x => x.Estate != null)
            .Where(x => x.OperationType != null)
            .Where(x => x.Employee.Id != EmployeeValueResolver.NonExistentEmployee.Id)
            .ToList();

        Console.WriteLine($"There are {totalEntitiesCount - entitiesToSave.Count} offers which are invalid.");

        return entitiesToSave;
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var estateTypes = await this.DbContext
            .Set<EstateType>()
            .ToListAsync();

        var estateCategories = await this.DbContext
            .Set<EstateCategory>()
            .ToListAsync();

        var furnitures = await this.DbContext
            .Set<Furniture>()
            .ToListAsync();

        var conditions = await this.DbContext
            .Set<Condition>()
            .ToListAsync();

        var dealMotives = await this.DbContext
            .Set<DealMotive>()
            .ToListAsync();

        var facingDirections = await this.DbContext
            .Set<FacingDirection>()
            .ToListAsync();

        var constructionTypes = await this.DbContext
            .Set<ConstructionType>()
            .ToListAsync();

        var completenessLevels = await this.DbContext
            .Set<CompletionLevel>()
            .ToListAsync();

        var countries = await this.DbContext
            .Set<Country>()
            .ToListAsync();

        var sourceCategories = await this.DbContext
            .Set<SourceCategory>()
            .ToListAsync();

        var operationTypes = await this.DbContext
            .Set<OperationType>()
            .ToListAsync();

        var populatedPlaceMappings = await this.DbContext
            .Set<PopulatedPlaceMapping>()
            .Include(ppm => ppm.PopulatedPlace)
            .ToListAsync();

        var provinceMappings = await this.DbContext
            .Set<ProvinceMapping>()
            .Include(pm => pm.Province)
            .ToListAsync();

        var municipalityMappings = await this.DbContext
            .Set<MunicipalityMapping>()
            .Include(pm => pm.Municipality)
            .ToListAsync();

        var districtMappings = await this.DbContext
            .Set<DistrictMapping>()
            .Include(pm => pm.District)
            .ToListAsync();

        var streetMappings = await this.DbContext
            .Set<StreetMapping>()
            .Include(pm => pm.Street)
            .ToListAsync();

        var company = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == UniqueEstatesCompanyName)
            .SingleAsync();

        var contractTypes = await this.DbContext
            .Set<ContractType>()
            .ToListAsync();

        var clientMappings = await this.DbContext
            .Set<ClientMapping>()
            .Include(cm => cm.Client)
            .ToListAsync();

        var employeeMappings = await this.DbContext
            .Set<EmployeeMapping>()
            .Include(cm => cm.Employee)
            .ToListAsync();

        var externalAgencyMappings = await this.DbContext
            .Set<ExternalAgencyMapping>()
            .Include(cm => cm.ExternalAgency)
            .ToListAsync();

        var heatingSystems = await this.DbContext
            .Set<Heating>()
            .ToListAsync();

        var garages = await this.DbContext
            .Set<Garage>()
            .ToListAsync();

        var websites = await this.DbContext
            .Set<Website>()
            .ToListAsync();

        var socialMedias = await this.DbContext
            .Set<SocialMedia>()
            .ToListAsync();

        var offerStatuses = await this.DbContext
            .Set<OfferStatus>()
            .ToListAsync();

        var archiveReasons = await this.DbContext
            .Set<ArchiveReason>()
            .ToListAsync();

        var vats = await this.DbContext
            .Set<Vat>()
            .ToListAsync();
        
        var houseTypes = await this.DbContext
            .Set<HouseType>()
            .ToListAsync();
        
        var leaseTerms = await this.DbContext
            .Set<LeaseTerm>()
            .ToListAsync();

        return new Dictionary<string, object>
        {
            { EstateCategoryValueResolver.EstateCategoriesItemsKey, estateCategories },
            { EstateTypeValueResolver.EstateTypesItemsKey, estateTypes },
            { FacingDirectionValueResolver.FacingDirectionsItemsKey, facingDirections },
            { ConstructionTypeValueResolver.ConstructionTypesItemsKey, constructionTypes },
            { DealMotiveValueResolver.DealMotivesItemsKey, dealMotives },
            { FurnitureValueResolver.FurnitureItemsKey, furnitures },
            { ConditionValueResolver.ConditionsItemsKey, conditions },
            { CompletionLevelValueResolver.CompletionLevelsItemsKey, completenessLevels },
            { CountryValueResolver.CountriesItemsKey, countries },
            { SourceCategoryValueResolver.SourceCategoriesItemsKey, sourceCategories },
            { SourceDetailOfferValueResolver.SourceCategoriesItemsKey, sourceCategories },
            { SourceDetailOfferValueResolver.ClientMappingsItemsKey, clientMappings },
            { SourceDetailOfferValueResolver.WebsitesItemsKey, websites },
            { SourceDetailOfferValueResolver.SocialMediasItemsKey, socialMedias },
            { OperationTypeValueResolver.OperationTypesItemsKey, operationTypes },
            { PopulatedPlaceValueResolver.PopulatedPlaceMappingsItemsKey, populatedPlaceMappings },
            { ProvinceValueResolver.ProvinceMappingsItemsKey, provinceMappings },
            { MunicipalityValueResolver.MunicipalityMappingsItemsKey, municipalityMappings },
            { DistrictValueResolver.DistrictMappingsItemsKey, districtMappings },
            { StreetValueResolver.StreetMappingsItemsKey, streetMappings },
            { TenantValueResolver<OfferUesImportModel, Offer>.CompanyNameItemsKey, company },
            { ContractTypeValueResolver.ContractTypesItemsKey, contractTypes },
            { ClientValueResolver.ClientsMappingsItemsKey, clientMappings },
            { EmployeeValueResolver.EmployeeMappingsItemsKey, employeeMappings },
            { ExternalAgencyValueResolver.ExternalAgenciesItemsKey, externalAgencyMappings },
            { GaragesValueResolver.GaragesItemsKey, garages },
            { HeatingSystemsValueResolver.HeatingSystemsItemsKey, heatingSystems },
            { OfferStatusValueResolver.OfferStatusesItemsKey, offerStatuses },
            { ArchiveReasonValueResolver.ArchiveReasonsItemsKey, archiveReasons },
            { ArchivedByValueResolver.EmployeeMappingsItemsKey, employeeMappings },
            { VatValueResolver.VatsItemsKey, vats },
            { LeaseTermValueResolver.LeaseTermsItemsKey, leaseTerms },
            { HouseTypeValueResolver.HouseTypesItemsKey, houseTypes }
        };
    }
}