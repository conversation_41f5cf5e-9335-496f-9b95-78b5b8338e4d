namespace RealtoCrm.DataImporter.Services.Implementation;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Clients;
using Companies;
using Employees;
using Mappings.DataResolvers;
using Mappings.DataResolvers.Addresses;
using Mappings.DataResolvers.Archive;
using Mappings.DataResolvers.Companies;
using Mappings.DataResolvers.Estates;
using Mappings.DataResolvers.Offers;
using Mappings.DataResolvers.Searches;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Models;
using Nomenclatures;
using Searches;

public class SearchesUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<SearchUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Search, SearchUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    private const int SearchStatusActiveId = 1;

    private const int SearchStatusArchiveId = 2;
    protected override string ImportDocumentUrl => DefaultUrl + "/searches";

    protected override IEnumerable<Search> ApplySpecifics(IEnumerable<Search> entities)
    {
        var totalEntitiesCount = entities.Count();

        var entitiesToSave = entities
            .Where(x => x.ClientId > 0)
            .Where(x => x.Client != null)
            .Where(x => x.EmployeeId is null or > 0);

        Console.WriteLine(
            $"There are {totalEntitiesCount - entitiesToSave.Count()} searches which have invalid client ID or employee ID and will not be saved.");

        return entitiesToSave;
    }

    protected override List<SearchUesImportModel> FilterAsiData(List<SearchUesImportModel> batchAsiData)
    {
        return batchAsiData
            .Where(s =>
                s.Countries?.Any() == true &&
                s.EstateTypes?.Any() == true &&
                s.Regions?.Any() == true &&
                s.Municipalities?.Any() == true &&
                s.Locations?.Any() == true &&
                s.IsDeletedImportEntry is not 1)
            .ToList();
    }
    
    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var furnitureList = await DbContext
            .Set<Furniture>()
            .ToListAsync();

        var company = await DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == UniqueEstatesCompanyName)
            .SingleAsync();

        var employeeMappings = await DbContext
            .Set<EmployeeMapping>()
            .Include(cm => cm.Employee)
            .ToListAsync();
        
        var districtMappings = await DbContext
            .Set<DistrictMapping>()
            .Include(cm => cm.District)
            .ToListAsync();

        var clientMappings = await DbContext
            .Set<ClientMapping>()
            .Include(cm => cm.Client)
            .ToListAsync();
        
        var populatedPlaceMappings = await DbContext
            .Set<PopulatedPlaceMapping>()
            .Include(cm => cm.PopulatedPlace)
            .ToListAsync();
        
        var provinceMappings = await DbContext
            .Set<ProvinceMapping>()
            .Include(cm => cm.Province)
            .ToListAsync();
        
        var municipalityMappings = await DbContext
            .Set<MunicipalityMapping>()
            .Include(cm => cm.Municipality)
            .ToListAsync();
        
        var externalAgencyMappings = await DbContext
            .Set<ExternalAgencyMapping>()
            .Include(cm => cm.ExternalAgency)
            .ToListAsync();

        var heatingSystems = await DbContext
            .Set<Heating>()
            .ToListAsync();

        var searchStatuses = await DbContext
            .Set<SearchStatus>()
            .ToListAsync();

        var archiveReasons = await DbContext
            .Set<ArchiveReason>()
            .ToListAsync();

        return new Dictionary<string, object>
        {
            { FurnitureValueResolver.FurnitureItemsKey, furnitureList },
            { TenantValueResolver<SearchUesImportModel, Search>.CompanyNameItemsKey, company },
            { EmployeeValueResolver.EmployeeMappingsItemsKey, employeeMappings },
            { HeatingSystemsValueResolver.HeatingSystemsItemsKey, heatingSystems },
            { ArchiveReasonValueResolver.ArchiveReasonsItemsKey, archiveReasons },
            { ArchivedByValueResolver.EmployeeMappingsItemsKey, employeeMappings },
            { SearchStatusValueResolver.SearchStatusesItemsKey, searchStatuses },
            { EmployeeIdValueResolver.EmployeeMappingsItemsKey, employeeMappings },
            { ClientValueResolver.ClientsMappingsItemsKey, clientMappings },
            { DistrictValueResolver.DistrictMappingsItemsKey, districtMappings },
            { PopulatedPlaceValueResolver.PopulatedPlaceMappingsItemsKey, populatedPlaceMappings },
            { ProvinceValueResolver.ProvinceMappingsItemsKey, provinceMappings },
            { MunicipalityValueResolver.MunicipalityMappingsItemsKey, municipalityMappings },
            { ExternalAgencyValueResolver.ExternalAgenciesItemsKey, externalAgencyMappings }
        };
    }
}