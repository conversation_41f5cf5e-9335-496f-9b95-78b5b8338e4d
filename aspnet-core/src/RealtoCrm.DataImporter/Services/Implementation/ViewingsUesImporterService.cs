using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Azure.Storage.Blobs.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using RealtoCrm.BlobStorage;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Searches;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Extensions;
using RealtoCrm.Files;
using RealtoCrm.Viewings;
using static RealtoCrm.CosherConsts.ContainerNames;
using ViewingFile = RealtoCrm.Viewings.ViewingFile;
using static RealtoCrm.CosherConsts.FileCategories;

namespace RealtoCrm.DataImporter.Services.Implementation;

public class ViewingsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IBlobStorageService blobStorageService,
    IJsonSerializerService<ViewingsUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService) : BaseUesImporterService<Viewing, ViewingsUesImportModel>(log,
    mapper, configuration, dbContextResolver, connectionStringResolver, jsonSerializerService,
    importDocumentFetchService, addressMappingService, mapMoneyService, mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/inspections";

    private static readonly HttpClient HttpClient = new()
    {
        Timeout = TimeSpan.FromMinutes(4)
    };

    protected override IEnumerable<Viewing> ApplySpecifics(IEnumerable<Viewing> entities)
        => entities
            .Where(x => x.OfferId > 0)
            .Where(x => x.SearchId > 0)
            .Where(x => x.StatusId > 0);

    protected override async Task DecorateBeforeAsync(List<Viewing> entityList,
        IEnumerable<ViewingsUesImportModel> batchAsiData)
    {
        var fileCategory = this.DbContext.FileCategories.First(x => x.Name == RegisterFileCategoryName).Id;
        var uesTenant = this.DbContext.Tenants.First(x => x.Name == UniqueEstatesCompanyName).Id;

        foreach (var viewing in entityList)
        {
            var correspondingModel = batchAsiData.First(b => b.Id == viewing.ViewingMapping.AdminId);

            if (correspondingModel.FilePath is null)
            {
                continue;
            }

            byte[] file;

            var fileName = $"viewing-for-offer-{viewing.OfferId}-and-search-{viewing.SearchId}";

            var blobImageSource = $"/{ViewingFilesContainerName}/{fileName}";

            var fileExistsInBlob = await blobStorageService.BlobExistsAsync(ViewingFilesContainerName, fileName);

            if (!fileExistsInBlob)
            {
                try
                {
                    file = await this.DownloadFileAsync(correspondingModel.FilePath);
                }
                catch (HttpRequestException e)
                {
                    log.Write($"Error fetching files for viewing {correspondingModel.Id}: {e.Message}");
                    continue;
                }

                using var fileStream = file.ToMemoryStream();

                blobImageSource = await blobStorageService.UploadFileAsync(
                    fileStream,
                    fileName,
                    ViewingFilesContainerName,
                    new BlobHttpHeaders
                    {
                        ContentType = "application/pdf"
                    });
            }

            viewing.ViewingsFiles.Add(new ViewingFile
            {
                File = new File
                {
                    FileName = fileName,
                    Source = blobImageSource,
                    CategoryId = fileCategory,
                    IsDeleted = false,
                    TenantId = uesTenant,
                }
            });
        }
    }

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var searchesMappings = await this.DbContext.SearchMappings.ToListAsync();
        var offerMappings = await this.DbContext.OfferMappings.ToListAsync();
        var offers = await this.DbContext.Offers.ToListAsync();

        return new Dictionary<string, object>
        {
            {
                OffersByOfferMappingsValueResolver.OffersByOfferMappingsItemsKey,
                offerMappings
            },
            {
                SearchBySearchesMappingValueResolver.SearchesMappingsItemsKey,
                searchesMappings
            },
            {
                ViewingStatusByStatusMappingValueResolver.ViewingStatusesItemsKey,
                await this.DbContext.ViewingStatuses.ToListAsync()
            },
            {
                SearchClientIdBySearchMappingValueResolver.SearchesItemsKey, await this.DbContext.Searches.ToListAsync()
            },
            { SearchClientIdBySearchMappingValueResolver.SearchesMappingItemsKey, searchesMappings },

            { OfferClientIdByOfferMappingValueResolver.OffersItemsKey, offers },
            { OfferClientIdByOfferMappingValueResolver.OffersByOfferMappingsItemsKey, offerMappings },
            { OfferEmployeeIdByOfferMappings.OffersByOfferMappingsItemsKey, offerMappings },
            { OfferEmployeeIdByOfferMappings.OfferEmployeeIdByOfferMappingsOffersKeys, offers },
            { SearchEmployeeBySearchMappings.SearchEmployeeBySearchMappingsItemsKey, searchesMappings }
        };
    }

    private async Task<byte[]> DownloadFileAsync(string url)
    {
        const int maxRetryAttempts = 3;
        const int delayInSeconds = 5;

        for (int attempt = 1; attempt <= maxRetryAttempts; attempt++)
        {
            try
            {
                var bytes = await HttpClient.GetByteArrayAsync(url);
                return bytes;
            }
            catch (HttpRequestException ex)
            {
                log.Write($"Attempt {attempt} failed to download {url}: {ex.Message}");
                if (attempt == maxRetryAttempts)
                {
                    throw;
                }
            }
            catch (TaskCanceledException ex)
            {
                log.Write($"Attempt {attempt} timed out for {url}: {ex.Message}");
                if (attempt == maxRetryAttempts)
                {
                    throw;
                }
            }

            await Task.Delay(TimeSpan.FromSeconds(delayInSeconds * attempt));
        }

        throw new Exception($"Failed to download image after {maxRetryAttempts} attempts: {url}");
    }
}