namespace RealtoCrm.DataImporter.Services.Implementation;

using System.Collections.Generic;
using System.Threading.Tasks;
using Models;
using Newtonsoft.Json;

public class JsonSerializerService<T>
    : IJsonSerializerService<T>
{
    public async Task<IEnumerable<T>?> DeserializeManyAsync(string json)
    {
        var wrapper = await Task.Run(() => JsonConvert.DeserializeObject<AsiApiResponseModel<T>>(json));

        return wrapper?.Data?.Data;
    }
    
    public async Task<IEnumerable<T>?> DeserializePageAsync(string json)
    {
        var wrapper = await Task.Run(() => 
            JsonConvert.DeserializeObject<UesApiSinglePageResponseModel<T>>(json)
        );

        return wrapper?.Data?.Values;
    }
    
    public async Task<IEnumerable<T>?> DeserializeOneAsync(string json)
    {
        var wrapper = await Task.Run(() => JsonConvert.DeserializeObject<AsiApiResponseModel<T>>(json));

        return wrapper?.Data?.Data;
    }

    public async Task<IEnumerable<T>?> DeserializeImageAsync(string json)
    {
        var wrapper = await Task.Run(() => JsonConvert.DeserializeObject<AsiImageResponseModel<T>>(json));

        return wrapper?.Data;
    }
}