namespace RealtoCrm.DataImporter.Services.Implementation;

using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Clients;
using Microsoft.Extensions.Configuration;
using Models;
using System.Collections.Generic;
using System.Linq;
using Abp.Extensions;
using System.Threading.Tasks;
using Companies;
using Mappings.DataResolvers.Clients;
using Mappings.DataResolvers.Companies;
using Microsoft.EntityFrameworkCore;
using Nomenclatures;
using SourceCategories;
using Tags;

public class ClientsUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<ClientsUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<Client, ClientsUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/clients";

    private const int LastClientTypeId = 2;
    private const int ForeignNationality = 2;

    protected override IEnumerable<Client> ApplySpecifics(IEnumerable<Client> entities)
        => entities.Where(c =>
            !c.PersonalData!.FirstName.IsNullOrEmpty() && !c.PersonalData!.LastName.IsNullOrEmpty());

    protected override async Task<IEnumerable<KeyValuePair<string, object>>> GetMappingDependencies()
    {
        var tagCategories = await this.DbContext.Set<TagCategory>().ToListAsync();

        var tags = await this.DbContext.Set<Tag>().ToListAsync();

        var company = await this.DbContext
            .Set<Company>()
            .Include(c => c.Tenant)
            .Where(c => c.Name == UniqueEstatesCompanyName)
            .SingleAsync();

        var jobPositions = await this.DbContext.Set<JobPosition>().ToListAsync();

        var workplaces = await this.DbContext.Set<Workplace>().ToListAsync();

        var clientPreferences = await this.DbContext.Set<ClientPreference>().ToListAsync();

        var nationalities = await this.DbContext.Set<Nationality>().ToListAsync();

        var titles = await this.DbContext.Set<Title>().ToListAsync();

        var maritalStatuses = await this.DbContext.Set<MaritalStatus>().ToListAsync();

        var sectorMappings = this.DbContext.SectorMappings.AsNoTracking().ToList();

        var municipalityMappings = this.DbContext.MunicipalityMappings
            .Include(m => m.Municipality).AsNoTracking().ToList();

        return new Dictionary<string, object>
        {
            { ClientTagsValueResolver.TagCategoriesItemKey, tagCategories },
            { ClientTagsValueResolver.ExistingTagsItemKey, tags },
            { ClientJobPositionsValueResolver.JobPositionsItemsKey, jobPositions },
            { TenantValueResolver<ClientsUesImportModel, Client>.CompanyNameItemsKey, company },
            { ClientWorkplaceValueResolver.WorkplacesItemsKey, workplaces },
            { ClientPreferencesValueResolver.ClientPreferencesItemsKey, clientPreferences },
            { NationalityValueResolver.NationalitiesItemsKey, nationalities },
            { TitleValueResolver.TitlesItemsKey, titles },
            { MaritalStatusesValueResolver.MaritalStatusesItemsKey, maritalStatuses },
            { ClientSectorsValueResolver.SectorMappingsItemsKey, sectorMappings },
            { DocumentAuthorityValueResolver.MunicipalityMappingsItemsKey, municipalityMappings },
        };
    }
}