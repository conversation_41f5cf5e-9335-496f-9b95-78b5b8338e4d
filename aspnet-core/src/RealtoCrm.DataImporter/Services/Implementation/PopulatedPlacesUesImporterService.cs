using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Abp.Domain.Uow;
using Abp.EntityFrameworkCore;
using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Extensions;
using RealtoCrm.Nomenclatures;
using RealtoCrm.DataImporter.Services.Models;
using RealtoCrm.Nomenclatures.PopulatedPlaces.Models;

namespace RealtoCrm.DataImporter.Services.Implementation;

public class PopulatedPlacesUesImporterService(
    Log log,
    IMapper mapper,
    IConfiguration configuration,
    IDbContextResolver dbContextResolver,
    IConnectionStringResolver connectionStringResolver,
    IJsonSerializerService<PopulatedPlacesUesImportModel> jsonSerializerService,
    IImportDocumentFetchService importDocumentFetchService,
    IAddressMappingService addressMappingService,
    IMapMoneyService mapMoneyService,
    IMapSourceCategoryService mapSourceCategoryService)
    : BaseUesImporterService<PopulatedPlace, PopulatedPlacesUesImportModel>(
        log,
        mapper,
        configuration,
        dbContextResolver,
        connectionStringResolver,
        jsonSerializerService,
        importDocumentFetchService,
        addressMappingService,
        mapMoneyService,
        mapSourceCategoryService)
{
    protected override string ImportDocumentUrl => this.DefaultUrl + "/locations";

    public override async Task<int> Import()
    {
        log.Write("Started PopulatedPlacesMapping import...");

        var count = 0;
        var pageNumber = 1;

        Console.WriteLine("Fetching initial page");

        var firstPageQueryString = await this.ImportDocumentFetchService.FetchDocumentAsync(
            this.ImportDocumentUrl,
            this.ApiKey);
        var totalPagesCount = GetTotalPageCount(firstPageQueryString);

        this.DbContext.ChangeTracker.AutoDetectChangesEnabled = false;

        var municipalityMappings = await this.DbContext.MunicipalityMappings
            .Where(mm => mm.AdminId != null)
            .ToDictionaryAsync(mm => mm.AdminId, mm => mm.MunicipalityId);

        var populatedPlacesList = this.Mapper
            .ProjectTo<PopulatedPlaceResponseModel>(this.DbContext.PopulatedPlaces)
            .ToList();

        while (pageNumber <= totalPagesCount)
        {
            var remainingPages = totalPagesCount - pageNumber + 1;
            var currentBatchSize = remainingPages < BatchSize ? remainingPages : BatchSize;
            var startTime = DateTime.Now;

            var batchData = await this.GetEntitiesForPages(pageNumber, currentBatchSize).ToListAsync();

            if (!batchData.Any())
            {
                break;
            }

            foreach (var place in batchData)
            {
                if (!place.MunicipalityId.HasValue || !municipalityMappings.TryGetValue(place.MunicipalityId.Value, out var mainMunicipalityId))
                {
                    continue;
                }

                var existingPlace = populatedPlacesList
                    .FirstOrDefault(p => p.Name == place.Name && p.MunicipalityId == mainMunicipalityId);

                if (existingPlace != null)
                {
                    var existingMapping = await this.DbContext.PopulatedPlaceMappings
                        .FirstOrDefaultAsync(ppm => ppm.PopulatedPlaceId == existingPlace.Id);

                    if (existingMapping != null && existingMapping.AdminId == null)
                    {
                        existingMapping.AdminId = place.AsiId;
                        this.DbContext.Entry(existingMapping).State = EntityState.Modified;
                    }
                    else if (existingMapping == null)
                    {
                        var newMapping = new PopulatedPlaceMapping
                        {
                            PopulatedPlaceId = existingPlace.Id,
                            AdminId = place.AsiId
                        };
                        await this.DbContext.PopulatedPlaceMappings.AddAsync(newMapping);
                    }
                }
                else
                {
                    var newPopulatedPlace = new PopulatedPlace
                    {
                        Name = place.Name ?? string.Empty,
                        MunicipalityId = mainMunicipalityId,
                        Ekatte = place.Ekatte ?? string.Empty,
                        Code = place.Postcode ?? string.Empty,
                        Type = place.TypeId == 2 ? PopulatedPlaceType.Village : PopulatedPlaceType.Town,
                        IsActive = place.IsVisible,
                        CreationTime = DateTime.Now
                    };
                    await this.DbContext.PopulatedPlaces.AddAsync(newPopulatedPlace);
                    await this.DbContext.SaveChangesAsync();

                    var newMapping = new PopulatedPlaceMapping
                    {
                        PopulatedPlaceId = newPopulatedPlace.Id,
                        AdminId = place.AsiId
                    };
                    await this.DbContext.PopulatedPlaceMappings.AddAsync(newMapping);
                }
            }

            await this.DbContext.SaveChangesAsync();

            var duration = (DateTime.Now - startTime).TotalSeconds;
            Console.WriteLine($"{batchData.Count} Populated Places processed in {duration} seconds");

            count += batchData.Count;
            pageNumber += currentBatchSize;
        }

        log.Write("Fetched count: " + count);
        log.Write("Finished import.");

        return count;
    }
}
