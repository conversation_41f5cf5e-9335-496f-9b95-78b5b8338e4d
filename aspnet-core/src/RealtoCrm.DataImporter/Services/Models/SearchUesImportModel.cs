using RealtoCrm.DataImporter.Services.Models.Helpers;

namespace RealtoCrm.DataImporter.Services.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.DataImporter.Services.Mappings;
using Mapping;
using Money;
using Searches;
using Services.Mappings.DataResolvers;
using Services.Mappings.DataResolvers.Addresses;
using Services.Mappings.DataResolvers.Archive;
using Services.Mappings.DataResolvers.Companies;
using Services.Mappings.DataResolvers.Estates;
using Services.Mappings.DataResolvers.Offers;
using Services.Mappings.DataResolvers.Searches;

public class SearchUesImportModel : IMapTo<Search>, IMapTo<SearchDetail>, IMapExplicitly
{
    private const int SearchTypeBuyAsiId = 1;

    private const int EuroAsiValue = 1;

    private const int BgnAsiValue = 2;

    private const int FacingDirectionSouthId = 5;

    private const int GarageNomenclatureId = 1;

    private const int DefaultSourceCategoryId = 1;

    [JsonProperty("id")]
    public int? SearchAdminId { get; set; }

    [JsonProperty("client_id")]
    public int? ClientId { get; set; }

    [JsonProperty("user_id")]
    public int? UserId { get; set; }

    [JsonProperty("type_id")]
    public int? TypeId { get; set; }

    [JsonProperty("agency_id")]
    public int? AgencyId { get; set; }

    [JsonProperty("archive_reason_id")]
    public int? ArchiveReasonUesId { get; set; }

    [JsonProperty("archive_date_to")]
    public string? ArchiveDateTo { get; set; }

    [JsonProperty("date_to_call")]
    public string? DateToCall { get; set; }

    [JsonProperty("archive_by")]
    public int? ArchiveBy { get; set; }

    [JsonProperty("currency_id")]
    public int? CurrencyId { get; set; }

    [JsonProperty("price_from")]
    public decimal? PriceFrom { get; set; }

    [JsonProperty("price_to")]
    public decimal? PriceTo { get; set; }

    [JsonProperty("square_from")]
    public double? SquareFrom { get; set; }

    [JsonProperty("square_to")]
    public double? SquareTo { get; set; }

    [JsonProperty("source_id")]
    public int? SourceId { get; set; }

    [JsonProperty("source_detail_id")]
    public int? SourceDetailId { get; set; }

    [JsonProperty("bedrooms_from")]
    public int? BedroomsFrom { get; set; }

    [JsonProperty("bedrooms_to")]
    public int? BedroomsTo { get; set; }

    [JsonProperty("bathrooms_from")]
    public int? BathroomsFrom { get; set; }

    [JsonProperty("bathrooms_to")]
    public int? BathroomsTo { get; set; }

    [JsonProperty("terraces_from")]
    public int? TerracesFrom { get; set; }

    [JsonProperty("terraces_to")]
    public int? TerracesTo { get; set; }

    [JsonProperty("rooms_from")]
    public int? RoomsFrom { get; set; }

    [JsonProperty("rooms_to")]
    public int? RoomsTo { get; set; }

    [JsonProperty("building_year_from")]
    public int? BuildingYearFrom { get; set; }

    [JsonProperty("building_year_to")]
    public int? BuildingYearTo { get; set; }

    [JsonProperty("floor_from")]
    public int? FloorFrom { get; set; }

    [JsonProperty("floor_to")]
    public int? FloorTo { get; set; }

    [JsonProperty("elevator")]
    public int? Elevator { get; set; }

    [JsonProperty("garage")]
    public int? Garage { get; set; }

    [JsonProperty("attic")]
    public int? Attic { get; set; }

    [JsonProperty("south_exposure")]
    public int? SouthExposure { get; set; }

    [JsonProperty("deal_motive_id")]
    public int? DealMotiveId { get; set; }

    [JsonProperty("financing_type_id")]
    public int? FinancingTypeId { get; set; }

    [JsonProperty("contract_type")]
    public string? ContractType { get; set; }

    [JsonProperty("credit_center")]
    public int? CreditCenter { get; set; }

    [JsonProperty("created_at")]
    [JsonConverter(typeof(SafeDateTimeConverter))]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    [JsonConverter(typeof(SafeDateTimeConverter))]
    public DateTime? UpdatedAt { get; set; }

    [JsonProperty("created_by")]
    public int? CreatedBy { get; set; }

    [JsonProperty("updated_by")]
    public int? UpdatedBy { get; set; }

    [JsonProperty("countries")]
    public IEnumerable<UesObject>? Countries { get; set; }

    [JsonProperty("estate_types")]
    public IEnumerable<UesObject>? EstateTypes { get; set; }

    [JsonProperty("regions")]
    public IEnumerable<UesObject>? Regions { get; set; }

    [JsonProperty("municipalities")]
    public IEnumerable<UesObject>? Municipalities { get; set; }

    [JsonProperty("locations")]
    public IEnumerable<UesObject>? Locations { get; set; }

    [JsonProperty("quarters")]
    public IEnumerable<UesObject>? Quarters { get; set; }

    [JsonProperty("heating_types")]
    public UesObject[] HeatingTypes { get; set; } = default!;

    [JsonProperty("deleted")]
    public int? IsDeletedImportEntry { get; set; }

    [JsonProperty("furniture_types")]
    public UesObject[] FurnitureTypes { get; set; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
    {
        this.RegisterToSearchMappings(mapper);
        RegisterToSearchDetail(mapper);
    }

    public void RegisterToSearchMappings(IProfileExpression mapper)
        => mapper.CreateMap<SearchUesImportModel, Search>()
            .ForMember(dest => dest.Type, opt =>
                opt.MapFrom(src => src.TypeId == SearchTypeBuyAsiId ? SearchType.Buy : SearchType.Rent))
            .ForMember(dest => dest.AreaFrom, opt =>
                opt.MapFrom(src => src.SquareFrom ?? 0))
            .ForMember(dest => dest.AreaTo, opt =>
                opt.MapFrom(src => src.SquareTo ?? 0))
            .ForMember(c => c.SearchMapping, opt => opt.MapFrom(cm => new SearchMapping
            {
                AdminId = cm.SearchAdminId
            }))
            .ForMember(dest => dest.CreationTime, opt =>
                opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.LastModificationTime, opt =>
                opt.MapFrom(src => src.UpdatedAt))
            .ForMember(dest => dest.SearchesContractTypes, opt =>
            {
                opt.PreCondition(src => src.ContractType != null &&
                                        ContractTypeAsiImporterMappings.AsiContractTypeIdToContractTypeId.ContainsKey(
                                            src.ContractType));
                opt.MapFrom(src => new List<SearchContractType>
                {
                    new SearchContractType
                    {
                        ContractTypeId =
                            ContractTypeAsiImporterMappings.AsiContractTypeIdToContractTypeId[src.ContractType!]
                    }
                });
            })
            .ForMember(dest => dest.WithCreditCenter,
                opt => { opt.MapFrom(src => src.CreditCenter != null && src.CreditCenter == 1); })
            .ForMember(dest => dest.SearchesDealMotives, opt =>
            {
                opt.PreCondition(src => src.DealMotiveId != null &&
                                        DealMotivesAsiImporterMappings.AsiDealMotiveIdToDealMotiveId.ContainsKey(
                                            (int)src.DealMotiveId));
                opt.MapFrom(src => new List<SearchDealMotive>
                {
                    new SearchDealMotive
                    {
                        DealMotiveId =
                            DealMotivesAsiImporterMappings.AsiDealMotiveIdToDealMotiveId[(int)src.DealMotiveId!]
                    }
                });
            })
            .ForMember(dest => dest.SearchesFinancing, opt =>
            {
                opt.PreCondition(src => src.FinancingTypeId != null &&
                                        FinancingAsiImporterMappings.AsiFinancingTypeIdToFinancingId.ContainsKey(
                                            (int)src.FinancingTypeId));
                opt.MapFrom(src => new List<SearchFinancing>
                {
                    new SearchFinancing
                    {
                        FinancingId =
                            FinancingAsiImporterMappings.AsiFinancingTypeIdToFinancingId[(int)src.FinancingTypeId!]
                    }
                });
            })
            .ForMember(dest => dest.SearchesFacingDirections, opt =>
            {
                opt.PreCondition(src => src.SouthExposure == 1);
                opt.MapFrom(src => new List<SearchFacingDirection>
                {
                    new SearchFacingDirection
                    {
                        FacingDirectionId = FacingDirectionSouthId
                    }
                });
            })
            .ForMember(dest => dest.SearchesGarages, opt =>
            {
                opt.PreCondition(src => src.Garage == 1);
                opt.MapFrom(src => new List<SearchGarage>
                {
                    new SearchGarage
                    {
                        GarageId = GarageNomenclatureId
                    }
                });
            })
            .ForMember(dest => dest.SourceCategoryId, opt =>
            {
                opt.MapFrom(src =>
                    src.SourceDetailId != null &&
                    SourceCategoriesAsiImporterMappings.AsiSourceIdToSourceCategoryId.ContainsKey(
                        (int)src.SourceDetailId)
                        ? SourceCategoriesAsiImporterMappings.AsiSourceIdToSourceCategoryId[(int)src.SourceDetailId]
                        : DefaultSourceCategoryId);
            })
            .ForMember(dest => dest.TerracesCount, opt =>
                opt.MapFrom(src => CalculateTerracesCount(src.TerracesFrom, src.TerracesTo))
            )
            .ForMember(dest => dest.SearchesCountries, opt =>
            {
                opt.PreCondition(src => src.Countries != null && src.Countries.Any());
                opt.MapFrom(src => src.Countries!
                    .Where(country => CountryAsiImpoterMappings.AsiCountryIdToCountryId.ContainsKey(country.Id))
                    .Select(country => new SearchCountry
                    {
                        CountryId = CountryAsiImpoterMappings.AsiCountryIdToCountryId[country.Id]
                    })
                    .ToList());
            })
            .ForMember(dest => dest.SearchesEstateTypes, opt =>
            {
                opt.PreCondition(src => src.EstateTypes != null && src.EstateTypes.Any());
                opt.MapFrom(src => src.GetMappedEstateTypes()
                    .Select(mappedId => new SearchEstateType
                    {
                        EstateTypeId = mappedId
                    })
                    .ToList());
            })
            .ForMember(dest => dest.SearchesFurniture, opt =>
                opt.MapFrom<FurnitureValueResolver>())
            .ForMember(dest => dest.ArchiveReason, opt =>
                opt.MapFrom<ArchiveReasonValueResolver>())
            .ForMember(dest => dest.ArchiveBy, opt =>
                opt.MapFrom<ArchivedByValueResolver>())
            .ForMember(dest => dest.SearchStatus, opt =>
                opt.MapFrom<SearchStatusValueResolver>())
            .ForMember(dest => dest.SearchesHeating, opt =>
                opt.MapFrom<HeatingSystemsValueResolver>())
            .ForMember(dest => dest.ArchiveDate, opt =>
                opt.MapFrom<ArchivedDateValueResolver>())
            .ForMember(dest => dest.DateToCall, opt =>
                opt.MapFrom<DateToCallValueResolver>())
            .ForMember(
                dest => dest.SearchDetail, opt =>
                    opt.MapFrom(src => src))
            .ForMember(dest => dest.Tenant, opt => opt
                .MapFrom<TenantValueResolver<SearchUesImportModel, Search>>())
            .ForMember(
                dest => dest.Employee, opt =>
                    opt.MapFrom<EmployeeValueResolver>())
            .ForMember(dest => dest.CreatorUserId,
                opt =>
                    opt.MapFrom(new EmployeeIdValueResolver(nameof(SearchUesImportModel.CreatedBy))))
            .ForMember(dest => dest.LastModifierUserId,
                opt =>
                    opt.MapFrom(new EmployeeIdValueResolver(nameof(SearchUesImportModel.UpdatedBy))))
            .ForMember(dest => dest.MoneyFrom, opt =>
                opt.MapFrom(new NullableRoundedMoneyValueResolver<SearchUesImportModel, Search>(
                    nameof(SearchUesImportModel.PriceFrom),
                    nameof(SearchUesImportModel.CurrencyId),
                    false
                )))
            .ForMember(dest => dest.MoneyTo, opt =>
                opt.MapFrom(new NullableRoundedMoneyValueResolver<SearchUesImportModel, Search>(
                    nameof(SearchUesImportModel.PriceTo),
                    nameof(SearchUesImportModel.CurrencyId),
                    false
                )))
            .ForMember(
                dest => dest.Client,
                opt =>
                {
                    opt.PreCondition((src) => src.ClientId != null);
                    opt.MapFrom<ClientValueResolver>();
                })
            .ForMember(dest => dest.SearchesDistricts, opt =>
                opt.MapFrom<DistrictValueResolver>())
            .ForMember(dest => dest.SearchesMunicipalities, opt =>
                opt.MapFrom<MunicipalityValueResolver>())
            .ForMember(dest => dest.SearchesProvinces, opt =>
                opt.MapFrom<ProvinceValueResolver>())
            .ForMember(dest => dest.ExternalAgency, opt =>
                opt.MapFrom<ExternalAgencyValueResolver>())
            .ForMember(dest => dest.SearchesPopulatedPlaces, opt =>
                opt.MapFrom<PopulatedPlaceValueResolver>());


    private static void RegisterToSearchDetail(IProfileExpression mapper)
    {
        mapper.CreateMap<SearchUesImportModel, SearchDetail>()
            .ForMember(dest => dest.HasAttic, opt =>
                opt.MapFrom<NullableBooleanValueResolver<SearchUesImportModel, SearchDetail>, int?>(src => src.Attic))
            .ForMember(dest => dest.HasElevator, opt =>
                opt.MapFrom<NullableBooleanValueResolver<SearchUesImportModel, SearchDetail>, int?>(src =>
                    src.Elevator));
    }

    public List<int> GetMappedEstateTypes()
    {
        List<int> mappedTypes = new();

        if (EstateTypes != null)
        {
            foreach (var estateType in EstateTypes)
            {
                if (estateType.Id == 2) // Special case: Apartment needs dynamic mapping
                {
                    int minBedrooms = BedroomsFrom ?? 1; // Default to 1 if null
                    int maxBedrooms = BedroomsTo ?? minBedrooms; // If only one value exists, use it

                    for (int bedrooms = minBedrooms; bedrooms <= maxBedrooms; bedrooms++)
                    {
                        if (bedrooms == 0 || bedrooms == 1)
                            mappedTypes.Add(2); // Двустаен
                        else if (bedrooms == 2)
                            mappedTypes.Add(3); // Тристаен
                        else if (bedrooms == 3)
                            mappedTypes.Add(4); // Четиристаен
                        else if (bedrooms >= 4)
                            mappedTypes.Add(5); // Многостаен
                    }

                    continue; // Skip normal mapping for apartments
                }

                // Use the standard dictionary mapping for other types
                if (OfferUesImporterMappings.UesEstateTypeIdToEstateTypeId.TryGetValue(estateType.Id, out int mappedId))
                {
                    mappedTypes.Add(mappedId);
                }
                else
                {
                    mappedTypes.Add(6); // Default "Other" category
                }
            }
        }

        return mappedTypes.Distinct().ToList(); // Remove duplicates
    }


    private static int CalculateTerracesCount(int? terracesFrom, int? terracesTo)
    {
        if (terracesFrom.HasValue && terracesTo.HasValue)
        {
            return (int)Math.Ceiling((terracesFrom.Value + terracesTo.Value) / 2.0);
        }

        return terracesFrom ?? terracesTo ?? 0;
    }

    public class UesObject
    {
        [JsonProperty("id")]
        public int Id { get; set; }
    }
}