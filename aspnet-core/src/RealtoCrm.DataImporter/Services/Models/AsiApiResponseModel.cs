namespace RealtoCrm.DataImporter.Services.Models;

using System.Collections.Generic;

public class AsiImageResponseModel<T>
{
    public string? Message { get; set; }

    public IEnumerable<T>? Data { get; set; }
}

public class AsiApiResponseModel<T>
{
    public string? Message { get; set; }

    public ApiData<T>? Data { get; set; }
}

public class UesApiSingleResponseModel<T>
{
    public string? Message { get; set; }

    public T Data { get; set; } = default!;
}

public class UesApiSinglePageResponseModel<T>
{
    public string? Message { get; set; }
    
    public int? Count { get; set; }

    public Dictionary<string, T>? Data { get; set; }
}

public class ApiData<T>
{
    public int Total { get; set; }

    public int PerPage { get; set; }

    public int CurrentPage { get; set; }

    public int LastPage { get; set; }

    public string? NextPageUrl { get; set; }

    public string? PrevPageUrl { get; set; }

    public int From { get; set; }

    public int To { get; set; }

    public IEnumerable<T>? Data { get; set; }
}