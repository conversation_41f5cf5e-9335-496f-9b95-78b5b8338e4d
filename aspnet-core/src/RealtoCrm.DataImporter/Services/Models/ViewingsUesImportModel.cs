using System;
using System.Collections.Generic;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Offers;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Searches;
using RealtoCrm.DataImporter.Services.Mappings.DataResolvers.Viewings;
using RealtoCrm.Mapping;
using RealtoCrm.Viewings;

namespace RealtoCrm.DataImporter.Services.Models;

public class ViewingsUesImportModel : IMapTo<Viewing>, IMapFrom<Viewing>, IMapExplicitly
{
    public int Total { get; set; }

    public int per_page { get; set; }

    public int current_page { get; set; }

    public int last_page { get; set; }

    public string next_page_url { get; set; }

    public object prev_page_url { get; set; }

    public int From { get; set; }

    public int To { get; set; }

    public int Id { get; set; }

    public int type_id { get; set; }

    public int? match_id { get; set; }

    [JsonProperty("client_id")] public int? ClientId { get; set; }

    [JsonProperty("offer_id")] public int OfferId { get; set; }

    public object project_id { get; set; }

    [JsonProperty("user_id")] public int? UserId { get; set; }

    public object agency_id { get; set; }

    public int? opposite_user_id { get; set; }

    public int Status { get; set; }

    public int? price { get; set; }

    public string Date { get; set; }

    public string date_done { get; set; }

    public string firm { get; set; }

    public string? Comment { get; set; }

    public int mobile { get; set; }

    [JsonProperty("created_at")]
    public DateTime? CreatedAt { get; set; }

    [JsonProperty("updated_at")]
    public DateTime? UodatedAt { get; set; }

    public object created_by { get; set; }

    public object updated_by { get; set; }
    
    
    [JsonProperty("deleted")]
    public int IsDeleted { get; set; }

    [JsonProperty("file_path")]
    public string? FilePath { get; set; }

    public ViewingFileRegister? Register { get; set; }

    public UesMatch? Match { get; set; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper.CreateMap<ViewingsUesImportModel, Viewing>()
            .ForMember(dest => dest.OfferId, opt =>
                opt.MapFrom<OffersByOfferMappingsValueResolver>())
            .ForMember(dest => dest.SearchId, opt =>
                opt.MapFrom<SearchBySearchesMappingValueResolver>())
            .ForMember(dest => dest.StatusId, opt =>
                opt.MapFrom<ViewingStatusByStatusMappingValueResolver>())
            .ForMember(dest => dest.SearchClientId, opt =>
                opt.MapFrom<SearchClientIdBySearchMappingValueResolver>())
            .ForMember(dest => dest.OfferClientId, opt =>
                opt.MapFrom<OfferClientIdByOfferMappingValueResolver>())
            .ForMember(dest => dest.OfferEmployeeId, opt =>
                opt.MapFrom<OfferEmployeeIdByOfferMappings>())
            .ForMember(dest => dest.SearchEmployeeId, opt =>
                opt.MapFrom<SearchEmployeeBySearchMappings>())
            .ForMember(dest => dest.StartDate, opt =>
                opt.MapFrom(src => src.Date))
            .ForMember(dest => dest.CreationTime, opt =>
                opt.MapFrom(src => src.CreatedAt))
            .ForMember(dest => dest.LastModificationTime, opt =>
                opt.MapFrom(src => src.UodatedAt))
            .ForMember(src => src.IsDeleted, opt =>
                opt.MapFrom(src => src.IsDeleted == 1))
            .ForMember(dest => dest.ViewingMapping, opt =>
                opt.MapFrom(src => new ViewingMapping
                {
                    AdminId = src.Id
                }))
            .ForMember(dest => dest.ViewingsComments, opt =>
                opt.MapFrom<ViewingCommentValueResolver>())
            .ForMember(dest => dest.Match, 
                opt => opt.MapFrom<MatchByViewingValueResolver>())
            .ForMember(dest => dest.MatchId, 
                opt => opt.Ignore())
            .ForMember(dest => dest.Id, opt => opt.Ignore())
            .ForMember(dest => dest.Status, 
                opt => opt.Ignore());
}


public class ViewingFileRegister
{
    public ViewingFile? File { get; set; }
}

public class ViewingFile
{
    public string Name { get; set; }
    
    public string Ext { get; set; }
}


public class UesMatch
{
    public int id { get; set; }

    public int offer_id { get; set; }

    [JsonProperty("client_search_id")] public int? ClientSearchId { get; set; }

    public int? Status { get; set; }

    public object created_at { get; set; }

    public string updated_at { get; set; }

    public object created_by { get; set; }

    public object updated_by { get; set; }

    public int deleted { get; set; }
}