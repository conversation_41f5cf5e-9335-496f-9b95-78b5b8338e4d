using System;
using AutoMapper;
using Newtonsoft.Json;
using RealtoCrm.Mapping;
using RealtoCrm.Nomenclatures;

namespace RealtoCrm.DataImporter.Services.Models;

public class PopulatedPlacesUesImportModel : IMapFrom<PopulatedPlace>, IMapExplicitly
{
    [JsonProperty("id")]
    public int AsiId { get; set; }

    public string? Name { get; set; }

    [JsonProperty("municipality_id")]
    public int? MunicipalityId { get; set; }

    [JsonProperty("type_id")]
    public int? TypeId { get; set; }
    
    [JsonProperty("is_visible")]
    public bool IsVisible { get; set; }

    public string? Postcode { get; set; } = default!;

    public string? Ekatte { get; set; } = default!;

    public string? CreatedAt { get; set; }

    public string? UpdatedAt { get; set; }


    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<PopulatedPlacesUesImportModel, PopulatedPlace>()
            .ForMember(dest => dest.Name, cfg => cfg
                .MapFrom(src => src.Name))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => src.IsVisible))
            .ForMember(dest => dest.Ekatte, cfg => cfg
                .MapFrom(src => src.Ekatte ?? string.Empty))
            .ForMember(dest => dest.Code, cfg => cfg
                .MapFrom(src => src.Postcode ?? string.Empty))
            .ForMember(dest => dest.Latitude, cfg => cfg
                .MapFrom(src => 0.0))
            .ForMember(dest => dest.Longitude, cfg => cfg
                .MapFrom(src => 0.0))
            .ForMember(dest => dest.Type, cfg => cfg
                .MapFrom(src => src.TypeId == 2 ? PopulatedPlaceType.Village : PopulatedPlaceType.Town))
            .ForMember(dest => dest.CreationTime, cfg => cfg
                .MapFrom(src =>
                    string.IsNullOrEmpty(src.CreatedAt) ? DateTime.MinValue : DateTime.Parse(src.CreatedAt)))
            .ForMember(dest => dest.LastModificationTime, cfg => cfg
                .MapFrom(src => string.IsNullOrEmpty(src.UpdatedAt) ? (DateTime?)null : DateTime.Parse(src.UpdatedAt)))
            .ForMember(dest => dest.IsActive, cfg => cfg
                .MapFrom(src => true))
            .ForMember(dest => dest.SearchesPopulatedPlaces, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.MunicipalityId, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Addresses, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Districts, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Streets, cfg => cfg
                .Ignore())
            .ForMember(dest => dest.Id, cfg => cfg
                .Ignore());
}