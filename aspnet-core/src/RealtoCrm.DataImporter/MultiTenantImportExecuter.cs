namespace RealtoCrm.DataImporter;

using System;
using System.Threading.Tasks;
using Abp.Data;
using Abp.Dependency;
using Abp.Domain.Uow;
using Abp.Extensions;
using Abp.MultiTenancy;
using Services.Implementation;
using Services.Implementation.Offers;

public class MultiTenantImportExecuter(
    Log log,
    OfferImageAsiImporterService offerImageAsiImporterService,
    OfferImageUesImporterService offerImageUesImporterService,
    OffersNewEstatesImporterService offersNewEstatesImporterService,
    OfferTranslationUesImporterService offerTranslationUesImporterService,
    EmployeesAsiImporterService employeesAsiImporterService,
    EmployeesUesImporterService employeesUesImporterService,
    EmployeesFortonImporterService employeesFortonImporterService,
    ClientsAsiImporterService clientsAsiImporterService,
    ClientsUesImporterService clientsUesImporterService,
    ClientsNewEstatesImporterService clientsNewEstatesImporterService,
    SectorMappingAsiImporterService sectorMappingAsiImporterService,
    SectorMappingUesImporterService sectorMappingUesImporterService,
    JobPositionsAsiImporterService jobPositionsAsiImporterService,
    DivisionsAsiImporterService divisionsAsiImporterService,
    DivisionsUesImporterService divisionsUesImporterService,
    DivisionsFortonImporterService divisionsFortonImporterService,
    DivisionsNewEstateImporterService divisionsNewEstateImporterService,
    TeamsAsiImporterService teamsAsiImporterService,
    TeamsFortonImporterService teamsFortonImporterService,
    TeamsNewEstatesImporterService teamsNewEstatesImporterService,
    ProvinceMappingsAsiImporterService provinceMappingsAsiImporterService,
    ProvinceMappingsUesImporterService provinceMappingsUesImporterService,
    MunicipalitiesAsiImporterService municipalitiesAsiImporterService,
    MunicipalitiesUesImporterService municipalitiesUesImporterService,
    WorkplacesAsiImporterService workplacesAsiImporterService,
    WorkplacesUesImporterService workplacesUesImporterService,
    PopulatedPlacesAsiImporterService populatedPlacesAsiImporterService,
    PopulatedPlacesUesImporterService populatedPlacesUesImporterService,
    DistrictsAsiImporterService districtsAsiImporterService,
    DistrictsUesImporterService districtsUesImporterService,
    StreetsAsiImporterService streetsAsiImporterService,
    StreetsUesImporterService streetsUesImporterService,
    OfficesAsiImporterService officesAsiImporterService,
    OfficesUesImporterService officesUesImporterService,
    OfficesFortonImporterService officesFortonImporterService,
    OfficeNewEstateImporterService officeNewEstateImporterService,
    ExternalAgenciesAsiImporterService externalAgenciesAsiImporterService,
    ExternalAgenciesUesImporterService externalAgenciesUesImporterService,
    DepartmentsAsiImporterService departmentsAsiImporterService,
    DepartmentsFortonImporterService departmentsFortonImporterService,
    OffersAsiImporterService offersAsiImporterService,
    OffersUesImporterService offersUesImporterService,
    SearchesAsiImporterService searchesAsiImporterService,
    SearchesUesImporterService searchesUesImporterService,
    DepartmentsUesImporterService departmentsUesImporterService,
    DepartmentsNewEstatesImporterService departmentsNewEstatesImporterService,
    EmployeesNewEstatesImporterService employeesNewEstatesImporterService,
    TeamsUesImporterService teamsUesImporterService,
    RelatedClientsUesImporterService relatedClientsUesImporterService,
    RelatedClientsNewEstatesImporterService relatedClientsNewEstatesImporterService,
    EmployeeUesAdditionsImporterService employeeUesAdditionsImporterService,
    DepartmentsUesAdditionsImporterService departmentsUesAdditionsImporterService,
    TeamsUesAdditionsImporterService teamsUesAdditionsImporterService,
    ClientSourceCategoriesUesImporterService clientSourceCategoriesUesImporterService,
    ClientSourceCategoriesNewEstatesImporterService clientSourceCategoriesNewEstatesImporterService,
    IDbPerTenantConnectionStringResolver connectionStringResolver,
    AdministrativeDivisionTranslationsImporterService administrativeDivisionTranslationsImporterService,
    RelatedClientsAsiImporterService relatedClientsAsiImporterService,
    ExternalSearchesUesImporterService externalSearchesUesImporterService,
    EmployeesNewEstatesTurboMappingImporterService turboMappingImporterService,
    SearchesNewEstatesAdminImporterService searchesNewEstatesAdminImporterService,
    ViewingsUesImporterService viewingsUesImporterService)
    : ITransientDependency
{
    public Log Log { get; } = log;

    public async Task Run(bool skipConnVerification, bool isDockerEnabled = false)
    {
        var hostConnStr = connectionStringResolver.GetNameOrConnectionString(
            new ConnectionStringResolveArgs(MultiTenancySides.Host));

        if (hostConnStr.IsNullOrWhiteSpace())
        {
            this.Log.Write("Configuration file should contain a connection string named 'Default'");
            return;
        }

        this.Log.Write("Host database: " + ConnectionStringHelper.GetConnectionString(hostConnStr));

        this.Log.Write("Select an option to import:");
        this.Log.Write("[0] Exit and cancel import");
        this.Log.Write("[ues] All UES Data (Contains ASI administrative divisions)");
        this.Log.Write("[ues-no-asi] All UES Data (WITHOUT ASI administrative divisions)");
        this.Log.Write("[asi] All ASI Data");
        this.Log.Write("[asi-base] All ASI Data without Clients, Offers, Searches");
        this.Log.Write("[asi-cos] ASI Data (Clients, Offers, Searches)");
        this.Log.Write("[forton] All CW Forton Data");
        this.Log.Write("[newestates] All New Estates Data");
        this.Log.Write("[everything] Import all from ASI and UES");
        this.Log.Write("[production] Production import (latest revision: 03.06.2025)");
        this.Log.Write("[2] Employees");
        this.Log.Write("[3] Clients");
        this.Log.Write("[4] Job Positions");
        this.Log.Write("[5] Divisions");
        this.Log.Write("[6] Teams");
        this.Log.Write("[7] Provinces, Municipalities, Populated Places, Districts and Streets");
        this.Log.Write("[8] Workplaces");
        this.Log.Write("[9] Offices");
        this.Log.Write("[10] External Agencies");
        this.Log.Write("[11] Departments");
        this.Log.Write("[12] Offers");
        this.Log.Write("[13] Offer images");
        this.Log.Write("[14] Sectors Mappings");
        this.Log.Write("[15] Related clients");
        this.Log.Write("[16] UES Employees");
        this.Log.Write("[17] Searches");
        this.Log.Write("[18] UES Provinces");
        this.Log.Write("[19] UES Municipalities");
        this.Log.Write("[20] UES Populated Places");
        this.Log.Write("[21] UES Districts");
        this.Log.Write("[22] UES Streets");
        this.Log.Write(
            "[23] UES Administrative Divisions (Provinces, Municipalities, Populated Places, Districts, Streets)");
        this.Log.Write("[24] UES Offices");
        this.Log.Write("[25] UES Workplaces");
        this.Log.Write("[26] UES Sectors");
        this.Log.Write("[27] UES Clients + Related clients and Source categories");
        this.Log.Write("[28] UES Offers");
        this.Log.Write("[29] UES Searches");
        this.Log.Write("[30] UES Departments");
        this.Log.Write("[31] UES Teams");
        this.Log.Write("[32] Forton Divisions");
        this.Log.Write("[33] Forton Offices");
        this.Log.Write("[34] Forton Departments");
        this.Log.Write("[35] Forton Teams");
        this.Log.Write("[36] Forton Employees");
        this.Log.Write("[37] UES External Agencies");
        this.Log.Write("[38] Divisions New Estates");
        this.Log.Write("[39] Offices New Estates");
        this.Log.Write("[40] Departments New Estates");
        this.Log.Write("[41] Teams New Estates");
        this.Log.Write("[42] Employees New Estates");
        this.Log.Write("[43] UES Offer Images");
        this.Log.Write("[44] UES Additional Data");
        this.Log.Write("[45] Administrative Division Translations");
        this.Log.Write("[46] UES External Searches");
        this.Log.Write("[47] UES Offer Translations");
        this.Log.Write("[48] New Estates To Admin Employee Turbo Mapping");
        this.Log.Write("[49] New Estates Admin Offers");
        this.Log.Write("[50] New Estates Admin Searches");
        this.Log.Write("[51] New Estates Admin Clients and Relations");
        this.Log.Write("[52] UES Viewings");
        this.Log.Write("Enter your choice: ");

        var choice = Console.ReadLine();

        switch (choice)
        {
            case "0":
                this.Log.Write("Exiting...");
                return;
            case "asi":
                await this.ImportAllAsi();
                break;
            case "asi-base":
                await this.ImportAsiBase();
                break;
            case "asi-cos":
                await this.ImportAsiClientsOffersSearches();
                break;
            case "ues":
                await this.ImportAllUes();
                break;
            case "ues-no-asi":
                await this.ImportAllUesOnly();
                break;
            case "forton":
                await this.ImportAllForton();
                break;
            case "newestates":
                await this.ImportAllNewEstates();
                break;
            case "2":
                await employeesAsiImporterService.Import();
                break;
            case "3":
                await clientsAsiImporterService.Import();
                break;
            case "4":
                await jobPositionsAsiImporterService.Import();
                break;
            case "5":
                await divisionsAsiImporterService.Import();
                break;
            case "6":
                await teamsAsiImporterService.Import();
                break;
            case "7":
                await provinceMappingsAsiImporterService.Import();
                await municipalitiesAsiImporterService.Import();
                await populatedPlacesAsiImporterService.Import();
                await districtsAsiImporterService.Import();
                await streetsAsiImporterService.Import();
                break;
            case "8":
                await workplacesAsiImporterService.Import();
                break;
            case "9":
                await officesAsiImporterService.Import();
                break;
            case "10":
                await externalAgenciesAsiImporterService.Import();
                break;
            case "11":
                await departmentsAsiImporterService.Import();
                break;
            case "12":
                await offersAsiImporterService.Import();
                break;
            case "13":
                await offerImageAsiImporterService.Import();
                break;
            case "14":
                await sectorMappingAsiImporterService.Import();
                break;
            case "15":
                await relatedClientsAsiImporterService.Import();
                break;
            case "16":
                await employeesUesImporterService.Import();
                break;
            case "17":
                await searchesAsiImporterService.Import();
                break;
            case "18":
                await provinceMappingsUesImporterService.Import();
                break;
            case "19":
                await municipalitiesUesImporterService.Import();
                break;
            case "20":
                await populatedPlacesUesImporterService.Import();
                break;
            case "21":
                await districtsUesImporterService.Import();
                break;
            case "22":
                await streetsUesImporterService.Import();
                break;
            case "23":
                await this.ImportUesAdministrativeDivisions();
                break;
            case "24":
                await officesUesImporterService.Import();
                break;
            case "25":
                await workplacesUesImporterService.Import();
                break;
            case "26":
                await sectorMappingUesImporterService.Import();
                break;
            case "27":
                await clientsUesImporterService.Import();
                await relatedClientsUesImporterService.Import();
                await clientSourceCategoriesUesImporterService.Import();
                break;
            case "28":
                await offersUesImporterService.Import();
                break;
            case "29":
                await searchesUesImporterService.Import();
                break;
            case "30":
                await departmentsUesImporterService.Import();
                break;
            case "31":
                await teamsUesImporterService.Import();
                break;
            case "32":
                await divisionsFortonImporterService.Import();
                break;
            case "33":
                await officesFortonImporterService.Import();
                break;
            case "34":
                await departmentsFortonImporterService.Import();
                break;
            case "35":
                await teamsFortonImporterService.Import();
                break;
            case "36":
                await employeesFortonImporterService.Import();
                break;
            case "37":
                await externalAgenciesUesImporterService.Import();
                break;
            case "38":
                await divisionsNewEstateImporterService.Import();
                break;
            case "39":
                await officeNewEstateImporterService.Import();
                break;
            case "40":
                await departmentsNewEstatesImporterService.Import();
                break;
            case "41":
                await teamsNewEstatesImporterService.Import();
                break;
            case "42":
                await employeesNewEstatesImporterService.Import();
                break;
            case "43":
                await offerImageUesImporterService.Import();
                break;
            case "44":
                await departmentsUesAdditionsImporterService.Import();
                await teamsUesAdditionsImporterService.Import();
                await employeeUesAdditionsImporterService.Import();
                break;
            case "45":
                await administrativeDivisionTranslationsImporterService.Import();
                break;
            case "46":
                await externalSearchesUesImporterService.Import();
                break;
            case "47":
                await offerTranslationUesImporterService.Import();
                break;
            case "48":
                await turboMappingImporterService.Import();
                break;
            case "49":
                await offersNewEstatesImporterService.Import();
                break;
            case "50":
                await searchesNewEstatesAdminImporterService.Import();
                break;
            case "51":
                await clientsNewEstatesImporterService.Import();
                await relatedClientsNewEstatesImporterService.Import();
                await clientSourceCategoriesNewEstatesImporterService.Import();
                break;
            case "52":
                await viewingsUesImporterService.Import();
                break;
            case "ne-clients-sources":
                await clientSourceCategoriesNewEstatesImporterService.Import();
                break;
            case "nodistricts":
                await provinceMappingsUesImporterService.Import();
                await municipalitiesUesImporterService.Import();
                await populatedPlacesUesImporterService.Import();
                break;
            case "production":
                await this.ImportProduction();
                break;
            case "everything":
                await this.ImportEverything();
                break;
            default:
                this.Log.Write("Invalid choice. Please try again.");
                break;
        }

        this.Log.Write("Import has finished successfully! GG WP!");
    }

    private async Task ImportEverything()
    {
        await provinceMappingsAsiImporterService.Import();
        await municipalitiesAsiImporterService.Import();
        await populatedPlacesAsiImporterService.Import();
        await districtsAsiImporterService.Import();
        await streetsAsiImporterService.Import();
        await provinceMappingsUesImporterService.Import();
        await municipalitiesUesImporterService.Import();
        await populatedPlacesUesImporterService.Import();
        await districtsUesImporterService.Import();
        await streetsUesImporterService.Import();
        await divisionsAsiImporterService.Import();
        await divisionsUesImporterService.Import();
        await teamsAsiImporterService.Import();
        await officesAsiImporterService.Import();
        await officesUesImporterService.Import();
        await departmentsAsiImporterService.Import();
        await departmentsUesImporterService.Import();
        await teamsUesImporterService.Import();
        await sectorMappingAsiImporterService.Import();
        await sectorMappingUesImporterService.Import();
        await workplacesAsiImporterService.Import();
        await workplacesUesImporterService.Import();
        await employeesAsiImporterService.Import();
        await employeesUesImporterService.Import();
        await jobPositionsAsiImporterService.Import();
        await externalAgenciesAsiImporterService.Import();
        await externalAgenciesUesImporterService.Import();
        await clientsUesImporterService.Import();
        await relatedClientsUesImporterService.Import();
        await clientSourceCategoriesUesImporterService.Import();
        await offersUesImporterService.Import();
        await searchesUesImporterService.Import();
        await clientsAsiImporterService.Import();
        await relatedClientsAsiImporterService.Import();
        await offersAsiImporterService.Import();
        await searchesAsiImporterService.Import();
        await this.ImportAllForton();
        await this.ImportAllNewEstates();
        await offerImageUesImporterService.Import();
        await departmentsUesAdditionsImporterService.Import();
        await teamsUesAdditionsImporterService.Import();
        await employeeUesAdditionsImporterService.Import();
        await administrativeDivisionTranslationsImporterService.Import();
    }

    private async Task ImportAsiBase()
    {
        await provinceMappingsAsiImporterService.Import();
        await municipalitiesAsiImporterService.Import();
        await populatedPlacesAsiImporterService.Import();
        await districtsAsiImporterService.Import();
        await streetsAsiImporterService.Import();
        await divisionsAsiImporterService.Import();
        await teamsAsiImporterService.Import();
        await officesAsiImporterService.Import();
        await departmentsAsiImporterService.Import();
        await sectorMappingAsiImporterService.Import();
        await employeesAsiImporterService.Import();
        await workplacesAsiImporterService.Import();
        await jobPositionsAsiImporterService.Import();
        await externalAgenciesAsiImporterService.Import();
    }

    private async Task ImportAsiClientsOffersSearches()
    {
        await clientsAsiImporterService.Import();
        await relatedClientsAsiImporterService.Import();
        await offersAsiImporterService.Import();
        await searchesAsiImporterService.Import();
    }

    private async Task ImportAllAsi()
    {
        await provinceMappingsAsiImporterService.Import();
        await municipalitiesAsiImporterService.Import();
        await populatedPlacesAsiImporterService.Import();
        await districtsAsiImporterService.Import();
        await streetsAsiImporterService.Import();
        await divisionsAsiImporterService.Import();
        await teamsAsiImporterService.Import();
        await officesAsiImporterService.Import();
        await departmentsAsiImporterService.Import();
        await sectorMappingAsiImporterService.Import();
        await employeesAsiImporterService.Import();
        await workplacesAsiImporterService.Import();
        await jobPositionsAsiImporterService.Import();
        await externalAgenciesAsiImporterService.Import();
        await clientsAsiImporterService.Import();
        await relatedClientsAsiImporterService.Import();
        await offersAsiImporterService.Import();
        await searchesAsiImporterService.Import();
    }

    private async Task ImportAllUes()
    {
        await provinceMappingsAsiImporterService.Import();
        await municipalitiesAsiImporterService.Import();
        await populatedPlacesAsiImporterService.Import();
        await districtsAsiImporterService.Import();
        await streetsAsiImporterService.Import();
        await this.ImportUesAdministrativeDivisions();
        await officesUesImporterService.Import();
        await departmentsUesImporterService.Import();
        await teamsUesImporterService.Import();
        await employeesUesImporterService.Import();
        await workplacesUesImporterService.Import();
        await sectorMappingUesImporterService.Import();
        await clientsUesImporterService.Import();
        await relatedClientsUesImporterService.Import();
        await externalAgenciesUesImporterService.Import();
        await clientSourceCategoriesUesImporterService.Import();
        await offersUesImporterService.Import();
        await searchesUesImporterService.Import();
        await departmentsUesAdditionsImporterService.Import();
        await teamsUesAdditionsImporterService.Import();
        await employeeUesAdditionsImporterService.Import();
        await offerImageUesImporterService.Import();
        await administrativeDivisionTranslationsImporterService.Import();
        await externalSearchesUesImporterService.Import();
        await offerTranslationUesImporterService.Import();
    }
    
    private async Task ImportAllUesOnly()
    {
        await this.ImportUesAdministrativeDivisions();
        await officesUesImporterService.Import();
        await departmentsUesImporterService.Import();
        await teamsUesImporterService.Import();
        await employeesUesImporterService.Import();
        await workplacesUesImporterService.Import();
        await sectorMappingUesImporterService.Import();
        await clientsUesImporterService.Import();
        await relatedClientsUesImporterService.Import();
        await externalAgenciesUesImporterService.Import();
        await clientSourceCategoriesUesImporterService.Import();
        await offersUesImporterService.Import();
        await searchesUesImporterService.Import();
        await departmentsUesAdditionsImporterService.Import();
        await teamsUesAdditionsImporterService.Import();
        await employeeUesAdditionsImporterService.Import();
        await offerImageUesImporterService.Import();
        await administrativeDivisionTranslationsImporterService.Import();
        await viewingsUesImporterService.Import();
    }

    private async Task ImportUesAdministrativeDivisions()
    {
        await divisionsUesImporterService.Import();
        await provinceMappingsUesImporterService.Import();
        await municipalitiesUesImporterService.Import();
        await populatedPlacesUesImporterService.Import();
        await districtsUesImporterService.Import();
        await streetsUesImporterService.Import();
        await administrativeDivisionTranslationsImporterService.Import();
    }

    private async Task ImportAllForton()
    {
        await divisionsFortonImporterService.Import();
        await officesFortonImporterService.Import();
        await departmentsFortonImporterService.Import();
        await teamsFortonImporterService.Import();
        await employeesFortonImporterService.Import();
    }
    
    private async Task ImportAllNewEstates()
    {
        await divisionsNewEstateImporterService.Import();
        await officeNewEstateImporterService.Import();
        await departmentsNewEstatesImporterService.Import();
        await teamsNewEstatesImporterService.Import();
        await employeesNewEstatesImporterService.Import();
        await turboMappingImporterService.Import();
        await clientsNewEstatesImporterService.Import();
        await relatedClientsNewEstatesImporterService.Import();
        await offersNewEstatesImporterService.Import();
        await searchesNewEstatesAdminImporterService.Import();
    }

    private async Task ImportProduction()
    {
        // ASI administrative divisions
        
        await provinceMappingsAsiImporterService.Import();
        await municipalitiesAsiImporterService.Import();
        await populatedPlacesAsiImporterService.Import();
        await districtsAsiImporterService.Import();
        await streetsAsiImporterService.Import();
        
        // UES 
        
        await this.ImportUesAdministrativeDivisions();
        await officesUesImporterService.Import();
        await departmentsUesImporterService.Import();
        await teamsUesImporterService.Import();
        await employeesUesImporterService.Import();
        await workplacesUesImporterService.Import();
        await sectorMappingUesImporterService.Import();
        await clientsUesImporterService.Import();
        await relatedClientsUesImporterService.Import();
        await externalAgenciesUesImporterService.Import();
        await clientSourceCategoriesUesImporterService.Import();
        await offersUesImporterService.Import();
        await searchesUesImporterService.Import();
        await departmentsUesAdditionsImporterService.Import();
        await teamsUesAdditionsImporterService.Import();
        await employeeUesAdditionsImporterService.Import();
        await administrativeDivisionTranslationsImporterService.Import();
        await externalSearchesUesImporterService.Import();
        
        // NEW ESTATES
        
        await divisionsNewEstateImporterService.Import();
        await officeNewEstateImporterService.Import();
        await departmentsNewEstatesImporterService.Import();
        await teamsNewEstatesImporterService.Import();
        await employeesNewEstatesImporterService.Import();
        await turboMappingImporterService.Import();
        await clientsNewEstatesImporterService.Import();
        await relatedClientsNewEstatesImporterService.Import();
        await offersNewEstatesImporterService.Import();
        await searchesNewEstatesAdminImporterService.Import();
        
        // FORTON
        
        await divisionsFortonImporterService.Import();
        await officesFortonImporterService.Import();
        await departmentsFortonImporterService.Import();
        await teamsFortonImporterService.Import();
        await employeesFortonImporterService.Import();
        
        // Offer Translations from Admin
        await offerTranslationUesImporterService.Import();
        
        // Offer Images
        await offerImageUesImporterService.Import();
        
    }
}