namespace RealtoCrm.EntityFrameworkCore.Configurations.Deals;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Deals;

internal class DealParticipationConfiguration : IEntityTypeConfiguration<DealParticipation>
{
    public void Configure(EntityTypeBuilder<DealParticipation> builder)
    {
        builder
            .HasKey(dp => dp.Id);

        builder
            .Property(dp => dp.Side)
            .IsRequired();

        builder
            .Property(dp => dp.PaymentType)
            .IsRequired();

        builder
            .HasOne(dp => dp.Deal)
            .WithMany(d => d.DealParticipations)
            .HasForeignKey(dp => dp.DealId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(dp => dp.ExternalBroker)
            .WithMany(b => b.DealParticipations)
            .HasForeignKey(dp => dp.ExternalBrokerId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(dp => dp.RecommendationEmployee)
            .WithMany(r => r.DealParticipations)
            .HasForeignKey(dp => dp.RecommendationEmployeeId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .Property(dp => dp.PercentageOnContract)
            .IsRequired();

        builder
            .Property(dp => dp.PercentageOnNotary)
            .IsRequired();

        builder
            .Property(dp => dp.CommissionOnContractPercentage)
            .IsRequired();

        builder
            .OwnsMoney(dp => dp.Commission);

        builder
            .OwnsMoney(dp => dp.FinalCommission);

        builder
            .Property(dp => dp.FinalCommissionPercentage)
            .IsRequired();

        builder
            .Property(dp => dp.IsValid)
            .IsRequired();

        builder
            .Property(dp => dp.IsApprovedByManager)
            .IsRequired();

        builder
            .HasOne(dp => dp.Tenant)
            .WithMany()
            .HasForeignKey(dp => dp.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);
    }
}