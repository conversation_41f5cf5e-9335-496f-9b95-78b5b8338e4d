namespace RealtoCrm.EntityFrameworkCore.Configurations.Offers;

using Extensions;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using RealtoCrm.Offers;
using static ModelConstants.Common;
using static ModelConstants.Offer;

internal class OfferConfiguration : IEntityTypeConfiguration<Offer>
{
    public void Configure(EntityTypeBuilder<Offer> builder)
    {
        builder
            .HasKey(o => o.Id);

        builder
            .Property(o => o.Name)
            .HasMaxLength(MaxOfferNameLength);

        builder
            .Property(o => o.EstateDescription);

        builder
            .Property(o => o.BuildingDescription)
            .HasMaxLength(MaxOfferDescriptionLength);

        builder
            .Property(o => o.LocationDescription)
            .HasMaxLength(MaxOfferDescriptionLength);

        builder
            .Property(o => o.AdvantagesDescription)
            .HasMaxLength(MaxOfferDescriptionLength);

        builder
            .Property(o => o.DistributionDescription)
            .HasMaxLength(MaxOfferDescriptionLength);

        builder
            .Property(o => o.ConvenientTimeToView)
            .HasMaxLength(MaxDescriptionLength);

        builder
            .OwnsMoney(o => o.Price);

        builder
            .OwnsMoney(o => o.OldPrice);

        builder
            .OwnsMoney(o => o.RecommendedPrice);

        builder
            .OwnsMoney(o => o.SquareMetrePrice);

        builder
            .OwnsMoney(o => o.ComparativeMarketAnalysisPrice);

        builder
            .OwnsMoney(o => o.MaintenanceFee);

        builder
            .OwnsMoney(o => o.PriceSpa);

        builder
            .Property(o => o.PriceOnRequest)
            .IsRequired();

        builder
            .Property(o => o.HasKey)
            .IsRequired();

        builder
            .HasOne(o => o.Client)
            .WithMany(c => c.Offers)
            .HasForeignKey(o => o.ClientId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Employee)
            .WithMany(e => e.Offers)
            .HasForeignKey(o => o.EmployeeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Estate)
            .WithMany(e => e.Offers)
            .HasForeignKey(o => o.EstateId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Cascade);

        builder
            .HasOne(o => o.OperationType)
            .WithMany(ot => ot.Offers)
            .HasForeignKey(o => o.OperationTypeId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Project)
            .WithMany(b => b.Offers)
            .HasForeignKey(o => o.ProjectId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Furniture)
            .WithMany(f => f.Offers)
            .HasForeignKey(o => o.FurnitureId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Vat)
            .WithMany(v => v.Offers)
            .HasForeignKey(o => o.VatId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Lifestyle)
            .WithMany(l => l.Offers)
            .HasForeignKey(o => o.LifestyleId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.ContractType)
            .WithMany(ct => ct.Offers)
            .HasForeignKey(o => o.ContractTypeId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.OfferStatus)
            .WithMany(g => g.Offers)
            .HasForeignKey(o => o.OfferStatusId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.ExternalAgency)
            .WithMany(g => g.Offers)
            .HasForeignKey(o => o.ExternalAgencyId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.DealMotive)
            .WithMany(v => v.Offers)
            .HasForeignKey(o => o.DealMotiveId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.SourceCategory)
            .WithMany(sc => sc.Offers)
            .HasForeignKey(o => o.SourceCategoryId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.Tenant)
            .WithMany()
            .HasForeignKey(o => o.TenantId)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(o => o.ArchiveReason)
            .WithMany(ar => ar.Offers)
            .HasForeignKey(o => o.ArchiveReasonId)
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(s => s.OfferDetail)
            .WithOne(d => d.Offer)
            .HasForeignKey<OfferDetail>(d => d.Id)
            .IsRequired()
            .OnDelete(DeleteBehavior.Restrict);

        builder
            .HasOne(p => p.LinkedOffer)
            .WithMany(lp => lp.LinkedOffers)
            .HasForeignKey(p => p.LinkedOfferId)
            .IsRequired(false)
            .OnDelete(DeleteBehavior.Restrict);
    }
}