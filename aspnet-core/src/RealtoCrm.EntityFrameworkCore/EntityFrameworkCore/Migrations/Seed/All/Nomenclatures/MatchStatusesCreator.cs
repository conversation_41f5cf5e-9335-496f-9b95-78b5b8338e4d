namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Base;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;

public class MatchStatusesCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<MatchStatus>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<MatchStatus>>> ObjectData
        =>
        [
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "Вътрешно презентиран",
                }
            ],
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "Презентиран",
                }
            ],
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "За оглед",
                }
            ],
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "Огледан",
                }
            ],
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "Депозит",
                }
            ],
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "Предварителен",
                }
            ],
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "Изповядване",
                }
            ],
            [
                new SeedValue<MatchStatus>
                {
                    Name = nameof(MatchStatus.Name),
                    Value = "Отказан",
                }
            ],
        ];
}