namespace RealtoCrm.EntityFrameworkCore.Migrations.Seed.All.Nomenclatures;

using System.Collections.Generic;
using Base;
using Microsoft.EntityFrameworkCore;
using RealtoCrm.Nomenclatures;

public class BanksCreator(DbContext dbContext)
    : BaseMigrationNomenclaturesDbBuilder<Bank>(dbContext)
{
    protected override IEnumerable<IEnumerable<SeedValue<Bank>>> ObjectData
        =>
        [
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "БАНКА ДСК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "УНИКРЕДИТ БУЛБАНК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ЮРОБАНК БЪЛГАРИЯ",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ОБЕДИНЕНА БЪЛГАРСКА БАНКА",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ИНВЕСТБАНК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ПЪРВА ИНВЕСТИЦИОННА БАНКА",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ТЕКСИМ БАНК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ЦЕНТРАЛНА КООПЕРАТИВНА БАНКА",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "АЛИАНЦ БАНК БЪЛГАРИЯ",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "БЪЛГАРО-АМЕРИКАНСКА КРЕДИТНА БАНКА",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ТИ БИ АЙ БАНК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ПРОКРЕДИТ БАНК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ИНТЕРНЕШЪНЪЛ АСЕТ БАНК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ТЪРГОВСКА БАНКА Д",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "БЪЛГАРСКА БАНКА ЗА РАЗВИТИЕ",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ТОКУДА БАНК",
                }
            ],
            [
                new SeedValue<Bank>
                {
                    Name = nameof(Bank.Name),
                    Value = "ОБЩИНСКА БАНКА",
                }
            ],
        ];
}