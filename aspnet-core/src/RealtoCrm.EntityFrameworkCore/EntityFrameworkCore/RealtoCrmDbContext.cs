namespace RealtoCrm.EntityFrameworkCore;

using Abp.Zero.EntityFrameworkCore;
using Addresses;
using AddressesEstateCharacteristics;
using Authorization.Delegation;
using Authorization.Roles;
using Authorization.Users;
using Buildings;
using Calls;
using Clients;
using Comments;
using Companies;
using CompanyPositions;
using ContactDetails;
using Contracts;
using DealMotiveOperationTypes;
using Deals;
using Deposits;
using Editions;
using Employees;
using EstateObjects;
using Estates;
using Extensions;
using ExternalSearches;
using Files;
using FurnitureTypesEstateCategories;
using Images;
using Matches;
using Meetings;
using Microsoft.EntityFrameworkCore;
using MultiTenancy;
using MultiTenancy.Accounting;
using MultiTenancy.Payments;
using Nomenclatures;
using Offers;
using OffersEmployeesChangeHistory;
using OpenIddict.Applications;
using OpenIddict.Authorizations;
using OpenIddict.Scopes;
using OpenIddict.Tokens;
using Projects;
using PropertyToEstateCategorySettings;
using Searches;
using SourceCategories;
using Storage;
using Tags;
using Tasks;
using Viewings;
using ViewingStatus = Nomenclatures.ViewingStatus;
using Surveys;
using Translations;

public class RealtoCrmDbContext(DbContextOptions<RealtoCrmDbContext> options)
    : AbpZeroDbContext<Tenant, Role, User, RealtoCrmDbContext>(options), IOpenIddictDbContext
{
    public DbSet<Employee> Employees { get; set; } = default!;

    public DbSet<Deal> Deals { get; set; } = default!;

    public DbSet<DealHistory> DealHistories { get; set; } = default!;

    public DbSet<DealParticipant> DealParticipants { get; set; } = default!;

    public DbSet<DealParticipation> DealParticipations { get; set; } = default!;

    public DbSet<DealParticipationComment> DealParticipationsComments { get; set; } = default!;

    public DbSet<DealTenant> DealTenants { get; set; } = default!;

    public DbSet<Deposit> Deposits { get; set; } = default!;

    public DbSet<DepositFile> DepositsFiles { get; set; } = default!;

    public DbSet<DepositComment> DepositsComments { get; set; } = default!;

    public DbSet<ExternalSearch> ExternalSearches { get; set; } = default!;

    public DbSet<Search> Searches { get; set; } = default!;

    public DbSet<SearchCountry> SearchesCountries { get; set; } = default!;

    public DbSet<SearchGarage> SearchesGarages { get; set; } = default!;

    public DbSet<SearchEstateGroup> SearchesEstateGroups { get; set; } = default!;

    public DbSet<SearchConstructionType> SearchesConstructionTypes { get; set; } = default!;

    public DbSet<SearchProvince> SearchesProvinces { get; set; } = default!;

    public DbSet<SearchPopulatedPlace> SearchesPopulatedPlaces { get; set; } = default!;

    public DbSet<SearchEstateType> SearchesEstateTypes { get; set; } = default!;

    public DbSet<SearchOffer> SearchesOffers { get; set; } = default!;

    public DbSet<SearchFinancing> SearchesFinancing { get; set; } = default!;

    public DbSet<SearchDealMotive> SearchesDealMotives { get; set; } = default!;

    public DbSet<SearchContractType> SearchesContractTypes { get; set; } = default!;

    public DbSet<SearchRegulation> SearchesRegulations { get; set; } = default!;

    public DbSet<SearchHouseType> SearchesHouseTypes { get; set; } = default!;

    public DbSet<SearchHeating> SearchesHeating { get; set; } = default!;

    public DbSet<SearchCondition> SearchesConditions { get; set; } = default!;

    public DbSet<SearchFurniture> SearchesFurniture { get; set; } = default!;

    public DbSet<SearchCompletionLevel> SearchesCompletionLevels { get; set; } = default!;

    public DbSet<SearchDetail> SearchDetails { get; set; } = default!;

    public DbSet<SearchComment> SearchesComments { get; set; } = default!;

    public DbSet<SearchTask> SearchesTasks { get; set; } = default!;

    public DbSet<SearchNomenclatureOption> SearchNomenclatureOptions { get; set; } = default!;

    public DbSet<SearchLifestyle> SearchesLifestyles { get; set; } = default!;

    public DbSet<SearchFacingDirection> SearchesFacingDirections { get; set; } = default!;

    public DbSet<Offer> Offers { get; set; } = default!;

    public DbSet<OfferDetail> OfferDetails { get; set; } = default!;

    public DbSet<OfferFile> OffersFiles { get; set; } = default!;

    public DbSet<OfferImage> OffersImages { get; set; } = default!;

    public DbSet<OfferComment> OffersComments { get; set; } = default!;

    public DbSet<OfferTask> OffersTasks { get; set; } = default!;

    public DbSet<OfferImageMapping> OfferImageMappings { get; set; } = default!;

    public DbSet<OfferPriceChange> OffersPriceChanges { get; set; } = default!;

    public DbSet<Estate> Estates { get; set; } = default!;

    public DbSet<EstateFile> EstatesFiles { get; set; } = default!;

    public DbSet<EstateImage> EstatesImages { get; set; } = default!;

    public DbSet<EstateCategory> EstateCategories { get; set; } = default!;

    public DbSet<EstateType> EstateTypes { get; set; } = default!;

    public DbSet<EstateOfferMarketingType> EstateOffersMarketingTypes { get; set; } = default!;

    public DbSet<ExternalOffer> ExternalOffers { get; set; } = default!;

    public DbSet<EstateTypeMarketingType> EstateTypesMarketingTypes { get; set; } = default!;

    public DbSet<EstateDetail> EstateDetails { get; set; } = default!;

    public DbSet<Building> Buildings { get; set; } = default!;

    public DbSet<EstateGroup> EstateGroups { get; set; } = default!;

    public DbSet<EstateGroupDetail> EstateGroupDetails { get; set; } = default!;

    public DbSet<Project> Projects { get; set; } = default!;

    public DbSet<ProjectDetail> ProjectDetails { get; set; } = default!;

    public DbSet<ProjectFile> ProjectsFiles { get; set; } = default!;

    public DbSet<ProjectImage> ProjectsImages { get; set; } = default!;

    public DbSet<ProjectComment> ProjectsComments { get; set; } = default!;

    public DbSet<ProjectClient> ProjectsClients { get; set; } = default!;

    public DbSet<ProjectEstateType> ProjectsEstateTypes { get; set; } = default!;

    public DbSet<ProjectContractType> ProjectsContractTypes { get; set; } = default!;

    public DbSet<ProjectEmployee> ProjectsEmployees { get; set; } = default!;

    public DbSet<ProjectSourceCategory> ProjectsSourceCategories { get; set; } = default!;

    public DbSet<ProjectTask> ProjectsTasks { get; set; } = default!;

    public DbSet<Viewing> Viewings { get; set; } = default!;

    public DbSet<ViewingFile> ViewingsFiles { get; set; } = default!;

    public DbSet<ViewingComment> ViewingsComments { get; set; } = default!;

    public DbSet<Client> Clients { get; set; } = default!;

    public DbSet<ClientLegalEntity> ClientLegalEntities { get; set; } = default!;

    public DbSet<ClientPersonalData> ClientPersonalData { get; set; } = default!;

    public DbSet<ClientContactDetail> ClientContactDetails { get; set; } = default!;

    public DbSet<ClientTags> ClientsTags { get; set; } = default!;

    public DbSet<ClientRefferal> ClientRefferals { get; set; } = default!;

    public DbSet<ClientCardType> ClientsCardTypes { get; set; } = default!;

    public DbSet<ClientWorkplace> ClientsWorkplaces { get; set; } = default!;

    public DbSet<ClientComment> ClientsComments { get; set; } = default!;

    public DbSet<ClientTask> ClientsTasks { get; set; } = default!;

    public DbSet<ClientSourceCategory> ClientsSourceCategories { get; set; } = default!;

    public DbSet<ClientSector> ClientsSectors { get; set; } = default!;

    public DbSet<ContactDetail> ContactDetails { get; set; } = default!;

    public DbSet<ContactDetailType> ContactDetailTypes { get; set; } = default!;

    public DbSet<Comment> Comments { get; set; } = default!;

    public DbSet<Task> Tasks { get; set; } = default!;

    public DbSet<File> Files { get; set; } = default!;

    public DbSet<FileCategory> FileCategories { get; set; } = default!;

    public DbSet<Image> Images { get; set; } = default!;

    public DbSet<ImageCategory> ImageCategories { get; set; } = default!;

    public DbSet<ImageThumbnail> ImageThumbnails { get; set; } = default!;

    public DbSet<Company> Companies { get; set; } = default!;

    public DbSet<Division> Divisions { get; set; } = default!;

    public DbSet<DivisionMapping> DivisionMappings { get; set; } = default!;

    public DbSet<Department> Departments { get; set; } = default!;

    public DbSet<DepartmentMapping> DepartmentMappings { get; set; } = default!;

    public DbSet<DepartmentContactDetail> DepartmentContactDetails { get; set; } = default!;

    public DbSet<Office> Offices { get; set; } = default!;

    public DbSet<OfficeMapping> OfficeMappings { get; set; } = default!;

    public DbSet<OfficeContactDetail> OfficeContactDetails { get; set; } = default!;

    public DbSet<WorkingTime> WorkingTimes { get; set; } = default!;

    public DbSet<Team> Teams { get; set; } = default!;

    public DbSet<Tag> Tags { get; set; } = default!;

    public DbSet<TagCategory> TagCategories { get; set; } = default!;

    public DbSet<BuildingClass> BuildingClasses { get; set; } = default!;

    public DbSet<CompletionLevel> CompletionLevels { get; set; } = default!;

    public DbSet<Condition> Conditions { get; set; } = default!;

    public DbSet<ConstructionType> ConstructionTypes { get; set; } = default!;

    public DbSet<ConstructionPurpose> ConstructionPurposes { get; set; } = default!;

    public DbSet<FacingDirection> FacingDirections { get; set; } = default!;

    public DbSet<Heating> Heating { get; set; } = default!;

    public DbSet<WindowJoinery> WindowJoinery { get; set; } = default!;

    public DbSet<ArchiveReason> ArchiveReasons { get; set; } = default!;

    public DbSet<HouseType> HouseTypes { get; set; } = default!;

    public DbSet<Fence> Fences { get; set; } = default!;

    public DbSet<Financing> Financing { get; set; } = default!;

    public DbSet<DealMotive> DealMotives { get; set; } = default!;

    public DbSet<DealType> DealTypes { get; set; } = default!;

    public DbSet<DepositStatus> DepositStatuses { get; set; } = default!;

    public DbSet<Infrastructure> Infrastructures { get; set; } = default!;

    public DbSet<ContractType> ContractTypes { get; set; } = default!;

    public DbSet<Furniture> Furniture { get; set; } = default!;

    public DbSet<Garage> Garages { get; set; } = default!;

    public DbSet<LeaseTerm> LeaseTerms { get; set; } = default!;

    public DbSet<Lifestyle> Lifestyles { get; set; } = default!;

    public DbSet<OperationType> OperationTypes { get; set; } = default!;

    public DbSet<Regulation> Regulations { get; set; } = default!;

    public DbSet<Resort> Resorts { get; set; } = default!;

    public DbSet<SocialMedia> SocialMedia { get; set; } = default!;

    public DbSet<SearchStatus> SearchStatuses { get; set; } = default!;

    public DbSet<ProjectStatus> ProjectStatuses { get; set; } = default!;

    public DbSet<Vat> Vats { get; set; } = default!;

    public DbSet<ViewingStatus> ViewingStatuses { get; set; } = default!;

    public DbSet<Website> Websites { get; set; } = default!;

    public DbSet<Title> Titles { get; set; } = default!;

    public DbSet<Gender> Genders { get; set; } = default!;

    public DbSet<Nationality> Nationalities { get; set; } = default!;

    public DbSet<MaritalStatus> MaritalStatuses { get; set; } = default!;

    public DbSet<CardType> CardTypes { get; set; } = default!;

    public DbSet<Workplace> Workplaces { get; set; } = default!;

    public DbSet<WorkplaceMapping> WorkplaceMappings { get; set; } = default!;

    public DbSet<Match> Matches { get; set; } = default!;

    public DbSet<MatchStatus> MatchStatuses { get; set; } = default!;

    public DbSet<Address> Addresses { get; set; } = default!;

    public DbSet<Country> Countries { get; set; } = default!;

    public DbSet<Province> Provinces { get; set; } = default!;

    public DbSet<ProvinceMapping> ProvinceMappings { get; set; } = default!;

    public DbSet<Municipality> Municipalities { get; set; } = default!;

    public DbSet<MunicipalityMapping> MunicipalityMappings { get; set; } = default!;

    public DbSet<PopulatedPlace> PopulatedPlaces { get; set; } = default!;

    public DbSet<PopulatedPlaceMapping> PopulatedPlaceMappings { get; set; } = default!;

    public DbSet<District> Districts { get; set; } = default!;

    public DbSet<DistrictMapping> DistrictMappings { get; set; } = default!;

    public DbSet<Street> Streets { get; set; } = default!;

    public DbSet<StreetMapping> StreetMappings { get; set; } = default!;

    public DbSet<BuildingPurpose> BuildingPurposes { get; set; } = default!;

    public DbSet<ExternalAgency> ExternalAgencies { get; set; } = default!;

    public DbSet<ExternalAgencyMapping> ExternalAgencyMappings { get; set; } = default!;

    public DbSet<OfferStatus> OfferStatuses { get; set; } = default!;

    public DbSet<Sector> Sectors { get; set; } = default!;

    public DbSet<FloorPreference> FloorPreferences { get; set; } = default!;

    public DbSet<ClientType> ClientTypes { get; set; } = default!;

    public DbSet<SourceCategory> SourceCategories { get; set; } = default!;

    public DbSet<SourceDetail> SourceDetails { get; set; } = default!;

    public DbSet<SourceDetailSearch> SearchesSourceDetails { get; set; } = default!;

    public DbSet<SourceDetailClient> SourceDetailsClients { get; set; } = default!;

    public DbSet<SourceDetailOffer> SourceDetailsOffers { get; set; } = default!;

    public DbSet<SourceDetailProject> SourceDetailsProjects { get; set; } = default!;

    public DbSet<MarketingCampaign> MarketingCampaigns { get; set; } = default!;

    public DbSet<OpenIddictApplication> Applications { get; set; } = default!;

    public DbSet<OpenIddictAuthorization> Authorizations { get; set; } = default!;

    public DbSet<OpenIddictScope> Scopes { get; set; } = default!;

    public DbSet<OpenIddictToken> Tokens { get; set; } = default!;

    public DbSet<BinaryObject> BinaryObjects { get; set; } = default!;

    public DbSet<SubscribableEdition> SubscribableEditions { get; set; } = default!;

    public DbSet<SubscriptionPayment> SubscriptionPayments { get; set; } = default!;

    public DbSet<Invoice> Invoices { get; set; } = default!;

    public DbSet<SubscriptionPaymentExtensionData> SubscriptionPaymentExtensionDatas { get; set; } = default!;

    public DbSet<UserDelegation> UserDelegations { get; set; } = default!;

    public DbSet<RecentPassword> RecentPasswords { get; set; } = default!;

    public DbSet<SearchDistrict> SearchesDistricts { get; set; } = default!;

    public DbSet<SearchMunicipality> SearchesMunicipalities { get; set; } = default!;

    public DbSet<ClientMapping> ClientMappings { get; set; } = default!;

    public DbSet<OfferMapping> OfferMappings { get; set; } = default!;

    public DbSet<SearchMapping> SearchMappings { get; set; } = default!;

    public DbSet<RelatedClient> RelatedClients { get; set; } = default!;

    public DbSet<PropertyToEstateCategorySetting> PropertyToEstateCategorySettings { get; set; } = default!;

    public DbSet<RelatedClientType> RelatedClientTypes { get; set; } = default!;

    public DbSet<Meeting> Meetings { get; set; } = default!;

    public DbSet<MeetingStatus> MeetingStatuses { get; set; } = default!;

    public DbSet<ClientJobPosition> ClientsJobPositions { get; set; } = default!;

    public DbSet<ClientClientPreference> ClientsClientPreferences { get; set; } = default!;

    public DbSet<ClientAddress> ClientsAddresses { get; set; } = default!;

    public DbSet<ClientPreference> ClientsPreferences { get; set; } = default!;

    public DbSet<AddressType> AddressTypes { get; set; } = default!;

    public DbSet<JobPosition> JobPositions { get; set; } = default!;

    public DbSet<EmployeeMapping> EmployeeMappings { get; set; } = default!;

    public DbSet<DepartmentManagerUsersUesMapping> DepartmentManagerUsersUesMapping { get; set; } = default!;

    public DbSet<TeamTeamLeaderUsersUesMapping> TeamTeamLeaderUsersUesMapping { get; set; } = default!;

    public DbSet<SectorMapping> SectorMappings { get; set; } = default!;

    public DbSet<Call> Calls { get; set; } = default!;

    public DbSet<Contract> Contracts { get; set; } = default!;

    public DbSet<Survey> Surveys { get; set; }

    public DbSet<Bank> Banks { get; set; } = default!;

    public DbSet<CompanyPosition> CompanyPositions { get; set; } = default!;

    public DbSet<OfferEmployeesChangeHistory> OffersEmployeesChangeHistory { get; set; } = default!;

    public DbSet<DealMotiveOperationType> DealMotivesOperationTypes { get; set; } = default!;

    public DbSet<AddressToEstateCategoryCharacteristic> AddressesToEstateCategoryCharacteristics { get; set; } = default!;
    
    public DbSet<FurnitureTypeEstateCategory> FurnitureTypesEstateCategories { get; set; } = default!;
    
    public DbSet<CountryTranslation> CountryTranslations { get; set; } = default!;
    
    public DbSet<ProvinceTranslation> ProvinceTranslations { get; set; } = default!;

    public DbSet<MunicipalityTranslation> MunicipalityTranslations { get; set; } = default!;

    public DbSet<PopulatedPlaceTranslation> PopulatedPlaceTranslations { get; set; } = default!;
    
    public DbSet<DistrictTranslation> DistrictTranslations { get; set; } = default!;
    
    public DbSet<OfferTranslation> OfferTranslations { get; set; } = default!;
    
    public DbSet<MatchMapping> MatchesMappings { get; set; } = default!;
    
    public DbSet<ViewingMapping> ViewingMappings { get; set; } = default!;

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        modelBuilder
            .ConfigureOpenIddict()
            .AddDateTimeConverters()
            .ApplyConfigurationsFromAssembly(this.GetType().Assembly);
    }
}