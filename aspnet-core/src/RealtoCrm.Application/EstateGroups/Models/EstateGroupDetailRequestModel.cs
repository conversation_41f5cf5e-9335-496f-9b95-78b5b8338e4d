namespace RealtoCrm.EstateGroups.Models;

using EstateObjects;
using Mapping;

public class EstateGroupDetailRequestModel : IMapTo<EstateGroupDetail>
{
    public int? Id { get; init; }

    public double? KINTArea { get; init; }

    public double? UsefulFloorArea { get; init; }

    public double? DensityArea { get; init; }

    public double? PossibleArea { get; init; }

    public double? BuildupArea { get; init; }

    public double? PlotArea { get; init; }

    public int? HouseTypeId { get; init; }

    public int? InfrastructureId { get; init; }

    public int? FenceId { get; init; }

    public int? ConstructionTypeId { get; init; }

    public int? HeatingId { get; init; }

    public int? FurnitureId { get; init; }
}