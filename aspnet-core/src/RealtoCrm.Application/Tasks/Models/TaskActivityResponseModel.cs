namespace RealtoCrm.Tasks.Models;

using System;
using AutoMapper;
using Mapping;
using Offers;
using Searches;

public class TaskActivityResponseModel : IMapFrom<OfferTask>, IMapFrom<SearchTask>, IMapExplicitly
{
    public bool TaskIsDone { get; set; }

    public string? Text { get; set; }

    public int TaskId { get; set; }

    public DateTime CreationTime { get; set; }

    public string? EmployeeFirstName { get; init; } = default!;

    public string? EmployeeLastName { get; init; } = default!;

    public void RegisterMappings(IProfileExpression mapper)
    {
        mapper.CreateMap<SearchTask, TaskActivityResponseModel>()
            .ForMember(dest => dest.Text, opt
                => opt.MapFrom(src => src.Task.Text))
            .ForMember(dest => dest.EmployeeFirstName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.FirstName : null))
            .ForMember(dest => dest.EmployeeLastName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.LastName : null));

        mapper.CreateMap<OfferTask, TaskActivityResponseModel>()
            .ForMember(dest => dest.Text, opt
                => opt.MapFrom(src => src.Task.Text))
            .ForMember(dest => dest.EmployeeFirstName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.FirstName : null))
            .ForMember(dest => dest.EmployeeLastName, opt
                => opt.MapFrom(src => src.Task.Employee != null ? src.Task.Employee.LastName : null));
    }
}