namespace RealtoCrm.Clients;

using System;
using Abp.Linq.Extensions;
using Abp.UI;
using RealtoCrm.Tags.Models;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Abp.Runtime.Session;
using Addresses;
using Addresses.Models;
using Authorization;
using Comments.Models;
using CommentsTasks;
using Common.Attributes;
using ContactDetails.Models;
using DataCrudModels;
using Employees;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Models;
using Expressions;
using Extensions;
using Microsoft.AspNetCore.Mvc;
using Nomenclatures.PopulatedPlaces;
using Offers;
using Searches;
using SourceCategories;
using Specifications;
using Tags;
using Tasks.Models;
using static CosherConsts.ClientTypes;

public class ClientsAppService(
    IEmployeesAppService employeesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder,
    IAddressesAppService addressesService,
    ITagsAppService tagsService,
    IPopulatedPlacesAppService populatedPlacesAppService,
    IOffersAppService offersAppService,
    ISearchesAppService searchesAppService,
    ISourceCategoriesAppService sourceCategoriesAppService)
    : CommentsTasksAppService<
        int,
        Client,
        ClientComment,
        ClientTask,
        ClientRequestModel,
        ClientRequestModel,
        ClientsPaginatedRequestModel,
        ClientDetailsResponseModel,
        ClientListingResponseModel>(employeesAppService, dbContextProvider, expressionsBuilder), IClientsAppService
{
    private string personalSphere = "Лична сфера";

    protected override Specification<Client> GetSpecification(ClientsPaginatedRequestModel request)
        => new ClientByIdSpecification(request.SearchTerm)
            .Or(new ClientByNameSpecification(request.SearchTerm))
            .Or(new ClientByEmailOrPhoneSpecification(request.SearchPhoneOrEmail))
            .And(new ClientByFirstNameSpecification(request.FirstName))
            .And(new ClientByLastNameSpecification(request.LastName))
            .And(new ClientByEmailSpecification(request.Email))
            .And(new ClientByPhoneSpecification(request.Phone))
            .And(new ClientByCreationTimeSpecification(request.CreationTime))
            .And(new ClientByTypeSpecification(request.Type))
            .And(new ClientByClientOwnershipSpecification(
                request.ClientOwnership,
                this.AbpSession.UserId!.Value,
                this.Data.Teams.FirstOrDefault(t =>
                    t.Employees.Any(x => x.UserAccount.UserId == this.AbpSession.UserId!.Value))?.Id))
            .And(new ClientByClientStatusSpecification(request.ClientStatus, request.ClientOwnership));

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.ContactCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.ContactUpdate)
            .AddCustomAttribute(nameof(this.UpdateAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.ContactSoftDelete)
            .AddCustomAttribute(nameof(this.DeleteAsync), new RequireTenantAttribute())
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.ContactView)
            .ConfigureAuthorize(nameof(this.AddClientSourcesAsync))
            .ConfigureAuthorize(nameof(this.GetMatchDetailsByPhoneOrEmailAsync));

    protected override async Task<Specification<Client>> GetSpecificationAsync(ClientsPaginatedRequestModel request)
    {
        if (request.ClientOwnership is not null && request.ClientOwnership != ClientOwnership.My)
        {
            return await base.GetSpecificationAsync(request);
        }

        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        return new ClientByEmployeeSpecification(employeeId);
    }

    protected override async Task<IEnumerable<ClientListingResponseModel>> GetListingAsync(
        Specification<Client> specification,
        Expression<Func<Client, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<Client>> sortByExpressions,
        int skip = 0,
        int take = Int32.MaxValue)
    {
        var clients = await base.GetListingAsync(
            specification,
            filtersExpression,
            sortByExpressions,
            skip,
            take).ToListAsync();

        var clientIds = clients.Select(x => x.Id).ToList();

        var offers = await offersAppService.GetLastActiveOfferForClientsAsync(clientIds).ToListAsync();

        var searches =
            await searchesAppService.GetLastActiveSearchForClientsAsync(clientIds).ToListAsync();
        foreach (var client in clients)
        {
            client.Offer = offers.FirstOrDefault(o => o?.ClientId == client.Id);
            client.Search = searches.FirstOrDefault(s => s?.ClientId == client.Id);
        }

        return clients;
    }

    public override async Task<ClientDetailsResponseModel?> GetDetailsAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<ClientDetailsResponseModel>(this
                .AllAsNoTracking()
                .AsSplitQuery()
                .Where(n => n.Id.Equals(id))
                .Where(this.BuildTenantSpecification()))
            .FirstOrDefaultAsync();

    protected override IQueryable<Client> BuildDetailsQuery(int id)
        => this
            .AllAsNoTracking()
            .AsSplitQuery()
            .Where(n => n.Id.Equals(id))
            .Where(this.BuildTenantSpecification());

    public override async Task<int> CreateAsync(ClientRequestModel request)
    {
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        request.TenantId = this.GetTenantId();

        foreach (var sourceCategoryRequest in request.ClientsSourceCategories)
        {
            sourceCategoryRequest.EmployeeId = employeeId;
        }

        return await base.CreateAsync(request);
    }

    public override async Task<int> UpdateAsync(int id, ClientRequestModel request)
    {
        var entity = await this.GetAsync(id);

        if (entity is null)
        {
            throw new EntityNotFoundException($"{nameof(Client)} with id '{id}' is not found.");
        }

        await this.ClearClientDataAsync(id);

        if (request.ClientAddresses.Count > 0)
        {
            request.ClientAddresses = request.ClientAddresses
                .Where(x => x.CountryId.HasValue)
                .ToList();

            foreach (var address in request.ClientAddresses)
            {
                await this.HandleClientAddressAsync(address, request.TypeId);
            }
        }

        request.ClientsTags = await this.HandleClientTags(request);

        request.TenantId = this.GetTenantId();

        this.ObjectMapper.Map(request, entity);

        await this.Data.SaveChangesAsync();

        foreach (var rc in request.RelatedClients)
        {
            await this.HandleRelatedClientAsync(rc);
        }

        foreach (var relatedCompany in request.RelatedCompanies)
        {
            await this.HandleRelatedCompanyAsync(relatedCompany, request.EmployeeId, id);
        }

        var incomingRelatedCLientIds = request.RelatedCompanies
            .Where(x => x.Id.HasValue)
            .Select(x => x.Id)
            .Concat(request.RelatedClients
                .Where(x => x.Id.HasValue)
                .Select(x => x.Id))
            .ToList();

        var entriesToRemove = this.Data.RelatedClients
            .Where(x => x.ClientId == id && !incomingRelatedCLientIds.Contains(x.Id))
            .ToList();

        this.Data.RelatedClients.RemoveRange(entriesToRemove);

        entity.RelatedClients.AddRange(this.ObjectMapper.Map<List<RelatedClient>>(request.RelatedCompanies));
        entity.RelatedClients.AddRange(this.ObjectMapper.Map<List<RelatedClient>>(request.RelatedClients));

        await this.Data.SaveChangesAsync();
        return entity.Id;
    }

    private async Task<IEnumerable<ClientTagRequestModel>> HandleClientTags(ClientRequestModel request)
    {
        var clientsTags = request.ClientsTags;

        if (!request.ClientsTags.Any())
        {
            return clientsTags;
        }

        var requestTagNames = request.ClientsTags
            .Select(ct => ct.TagName)
            .ToList();

        var requestTagCategoriesIds = request.ClientsTags
            .Select(ct => ct.TagCategoryId)
            .ToList();

        var tags = await tagsService.GetAllByNamesAndCategories(requestTagNames, requestTagCategoriesIds);

        foreach (var clientTag in request.ClientsTags)
        {
            var existingTag = tags
                .FirstOrDefault(t => t.Name == clientTag.TagName.ToLower() && t.CategoryId == clientTag.TagCategoryId);

            var tagId = existingTag?.Id;

            if (existingTag is null)
            {
                tagId = await tagsService.CreateAsync(new TagsRequestModel
                {
                    Name = clientTag.TagName.ToLower(),
                    CategoryId = clientTag.TagCategoryId,
                });
            }

            clientTag.TagId = tagId;
        }

        return clientsTags;
    }

    public async Task AddClientSourcesAsync(ClientSourcesRequestModel request)
    {
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());

        var clientSourceCategory = this.ObjectMapper.Map<ClientSourceCategory>(request.SourceCategory);
        var sourceDetailClient = this.ObjectMapper.Map<SourceDetailClient>(request.SourceDetail);

        clientSourceCategory.EmployeeId = employeeId;
        sourceDetailClient.ClientId = request.SourceCategory.ClientId;

        await this.Data.AddAsync(clientSourceCategory);
        await this.Data.AddAsync(sourceDetailClient);

        await this.Data.SaveChangesAsync();
    }

    public async Task UpdateRelatedClientsAsync(int id, IEnumerable<ClientRelatedClientsRequestModel> request)
    {
        var client = await this.GetAsync(id);

        if (client is null)
        {
            throw new EntityNotFoundException(typeof(Client), id);
        }

        var relatedClientsToRemove = await this
            .Data
            .RelatedClients
            .Where(sd => sd.ClientId == id)
            .ToListAsync();

        this.Data.RelatedClients.RemoveRange(relatedClientsToRemove);

        foreach (var relatedClient in request)
        {
            await this.HandleRelatedClientAsync(relatedClient);
        }

        var newRelatedClients = this.ObjectMapper.Map<IEnumerable<RelatedClient>>(request);

        client.RelatedClients.AddRange(newRelatedClients);

        await this.Data.SaveChangesAsync();
    }

    private async Task HandleClientAddressAsync(ClientAddressesRequestModel requestAddress, int clientTypeId)
    {
        requestAddress.AddressTypeId = clientTypeId == (int)ClientTypeId.Personal
            ? (int)AddressTypesId.Personal
            : (int)AddressTypesId.Business;

        requestAddress.AddressId ??= await this.GetNewlyCreatedAddress(requestAddress);
    }

    private async Task<int> GetNewlyCreatedAddress(ClientAddressesRequestModel requestAddress)
    {
        var addressModel = new BaseAddressRequestModel();

        requestAddress.TenantId = this.GetTenantId();
        if (!requestAddress.MunicipalityId.HasValue && requestAddress.PopulatedPlaceId.HasValue)
        {
            var municipalityAndProvince =
                await populatedPlacesAppService.GetMunicipalityAndProvinceIdByPopulatedPlaceIdAsync(requestAddress
                    .PopulatedPlaceId.Value);

            requestAddress.MunicipalityId = municipalityAndProvince?.MunicipalityId;
            requestAddress.ProvinceId = municipalityAndProvince?.ProvinceId;
        }

        return await addressesService.CreateAsync(this.ObjectMapper.Map(requestAddress, addressModel));
    }

    public async Task<ICollection<TaskResponseModel>> GetTasksAsync(int clientId)
        => await this.ObjectMapper
            .ProjectTo<TaskResponseModel>(
                this.Data.ClientsTasks
                    .Where(ct => ct.ClientId == clientId)
                    .Select(ct => ct.Task)
            )
            .ToListAsync();

    public async Task<ICollection<CommentResponseModel>> GetCommentsAsync(int clientId)
        => await this.ObjectMapper
            .ProjectTo<CommentResponseModel>(
                this.Data.ClientsComments
                    .Where(ct => ct.ClientId == clientId)
                    .Select(ct => ct.Comment).OrderByDescending(x => x.CreationTime)
            )
            .ToListAsync();

    public async Task<ClientMatchDetailsResponseModel?> GetMatchDetailsByPhoneOrEmailAsync(string phoneNumberOrEmail)
    {
        var specification = this.BuildTenantSpecification()
            .And(new ClientByEmailOrPhoneSpecification(phoneNumberOrEmail));

        var client = await this.ObjectMapper
            .ProjectTo<ClientMatchDetailsResponseModel>(this
                .AllAsNoTracking()
                .Where(specification))
            .FirstOrDefaultAsync();

        if (client is null)
        {
            return null;
        }

        client.IsRelatedWithMe = await this.IsClientRelatedWithUser(client.Id, this.AbpSession.GetUserId());

        return client;
    }

    public async Task<ClientShortDetailsResponseModel?> GetDetailsByPhoneOrEmailOrUnifiedIdentificationCodeAsync(
        string phoneNumberOrEmailOrUnifiedIdentificationCode)
        => await this.ObjectMapper
            .ProjectTo<ClientShortDetailsResponseModel>(this
                .AllAsNoTracking()
                .Where(new ClientByIdSpecification(phoneNumberOrEmailOrUnifiedIdentificationCode)
                    .Or(new ClientByUnifiedIdentificationCodeSpecification(
                        phoneNumberOrEmailOrUnifiedIdentificationCode))
                    .Or(new ClientByEmailOrPhoneSpecification(phoneNumberOrEmailOrUnifiedIdentificationCode))))
            .FirstOrDefaultAsync();

    public async Task<int?> UpdateClientIdentificationNumberOrUnifiedIdentificationCode(ClientProjectUpdateModel model)
    {
        var client = await this.Data.Clients
            .Include(x => x.LegalEntity)
            .Include(x => x.PersonalData)
            .FirstOrDefaultAsync(x => x.Id == model.ClientId);

        if (client == null)
        {
            throw new UserFriendlyException("Client not found.");
        }

        if (client.TypeId == (int)ClientTypeId.Personal &&
            string.IsNullOrEmpty(client.PersonalData?.IdentificationNumber))
        {
            client.PersonalData!.IdentificationNumber = model.IdentificationNumber;
        }

        if (client.TypeId == (int)ClientTypeId.Legal &&
            string.IsNullOrEmpty(client.LegalEntity?.UnifiedIdentificationCode))
        {
            client.LegalEntity!.UnifiedIdentificationCode = model.UnifiedIdentificationCode;
        }

        this.Data.Update(client);
        await this.Data.SaveChangesAsync();
        return client.Id;
    }

    public async Task<IEnumerable<ContactDetailsExistenceResponseModel>> GetPhoneOrEmailExistenceAsync(
        IEnumerable<string> values,
        int clientId)
        => await this.ObjectMapper
            .ProjectTo<ContactDetailsExistenceResponseModel>(this
                .AllAsNoTracking()
                .Where(c => c.ContactDetails.Any(cd => cd.ClientId != clientId))
                .Where(this.BuildTenantSpecification()
                    .And(new ClientByPhoneNumbersOrEmailsSpecification(values)))
                .SelectMany(c => c.ContactDetails)
                .Where(c => values.Contains(c.Value)))
            .ToListAsync();

    public async Task<IEnumerable<ClientForRelatedClientResponseModel>> GetListingForRelatedClientAsync(
        string? phoneNumberOrEmail)
        => await this.ObjectMapper
            .ProjectTo<ClientForRelatedClientResponseModel>(this
                .AllAsNoTracking()
                .Where(new ClientByEmailOrPhoneSpecification(phoneNumberOrEmail))
                .Where(this.BuildTenantSpecification())
                .Take(count: 10))
            .ToListAsync();

    public async Task<ClientIdentificationDetailsResponseModel?> GetIdentificationDetailsAsync(int id)
        => await this.ObjectMapper
            .ProjectTo<ClientIdentificationDetailsResponseModel>(this
                .AllAsNoTracking()
                .Where(n => n.Id == id)
                .Where(this.BuildTenantSpecification()))
            .FirstOrDefaultAsync();

    [HttpPost]
    public async Task<IEnumerable<ClientForLinkedProjectResponseModel>> GetForLinkedProjectAsync(
        ClientsForLinkedProjectRequestModel request)
        => await this.ObjectMapper
            .ProjectTo<ClientForLinkedProjectResponseModel>(this
                .AllAsNoTracking()
                .Where(new ClientByTypeSpecification(LegalClientTypeName)
                    .And(new ClientByTenantsSpecification(request.TenantsIds))
                    .And(new ClientByEmailOrPhoneSpecification(request.SearchTerm))
                    .Or(new ClientByUnifiedIdentificationCodeSpecification(request.SearchTerm))))
            .ToListAsync();

    public  async Task<IEnumerable<ClientListingResponseModel>>  GetEmployeePersonalClientsByPhoneOrEmail(string phoneOrEmail) 
    {
        var employeeId = await employeesAppService.GetIdOrDefaultAsync(this.AbpSession.GetUserId());
        
        return this.All().Where(x => new clientbyphoneoremailspecification(phoneOrEmail));
    }


    public async Task<List<ClientLegalEntityResponseModel>> GetLegalEntitiesByUnifiedIdentificationCode(
        string? unifiedIdentificationCode, int pageSize)
        => await this.ObjectMapper.ProjectTo<ClientLegalEntityResponseModel>(this.AllAsNoTracking()
                .Where(x => x.LegalEntity != null)
                .WhereIf(
                    !string.IsNullOrEmpty(unifiedIdentificationCode),
                    x => x.LegalEntity!.UnifiedIdentificationCode != null &&
                         x.LegalEntity.UnifiedIdentificationCode.Contains(unifiedIdentificationCode)
                ).Select(x => x.LegalEntity)
                .Take(pageSize))
            .ToListAsync();

    private async Task HandleRelatedClientAsync(ClientRelatedClientsRequestModel request)
    {
        if (request.ClientRelatedClientId.HasValue)
        {
            return;
        }

        var isExisting = await this.IsExisting(request.PhoneNumber, request.Email);

        if (isExisting)
        {
            return;
        }

        request.ClientRelatedClientId = await this.CreateNewClientAsync(request);
    }

    private async Task HandleRelatedCompanyAsync(ClientRelatedCompaniesRequestModel request, int employeeId,
        int clientId)
    {
        if (request.RelatedClientId.HasValue)
        {
            return;
        }

        request.RelatedClientId = await this.CreateNewCompany(request, clientId, employeeId);
    }

    private async Task<bool> IsClientRelatedWithUser(int clientId, long userId)
        => await this.Data
            .ClientsSourceCategories
            .AsNoTracking()
            .AnyAsync(cs => cs.ClientId == clientId && cs.Employee.UserAccount.UserId == userId);

    private async Task<bool> IsExisting(string phoneNumber, string? email)
    {
        var phoneNumberOrEmail = !string.IsNullOrEmpty(phoneNumber) ? phoneNumber : email;
        return await this.Data.Clients.AnyAsync(client => client.ContactDetails
            .Any(cd => (cd.ContactDetailId == (int)ContactDetailId.Phone ||
                        cd.ContactDetailId == (int)ContactDetailId.Email)
                       && cd.Value == phoneNumberOrEmail));
    }


    private async Task ClearClientDataAsync(int id)
    {
        await this
            .Data
            .ClientsWorkplaces
            .Where(cw => cw.ClientId == id)
            .ExecuteDeleteAsync();

        await this
            .Data
            .ClientsJobPositions
            .Where(cw => cw.ClientId == id)
            .ExecuteDeleteAsync();

        await this
            .Data
            .ClientsCardTypes
            .Where(cw => cw.ClientId == id)
            .ExecuteDeleteAsync();

        await this
            .Data
            .SourceDetailsClients
            .Where(sd => sd.ClientId == id)
            .ExecuteDeleteAsync();

        await this.Data
            .ClientsClientPreferences
            .Where(ccp => ccp.ClientId == id)
            .ExecuteDeleteAsync();

        await this
            .Data
            .ClientsSectors
            .Where(sd => sd.ClientId == id)
            .ExecuteDeleteAsync();

        await this
            .Data
            .ClientsAddresses
            .Where(sd => sd.ClientId == id)
            .ExecuteDeleteAsync();

        await this
            .Data
            .ClientContactDetails
            .Where(sd => sd.ClientId == id)
            .ExecuteDeleteAsync();

        await this.Data.ClientsTags.Where(x => x.ClientId == id).ExecuteDeleteAsync();
    }

    public async Task<ClientByPhoneResponseModel?> FindByPhoneAsync(string phoneNumber)
        => await this.ObjectMapper.ProjectTo<ClientByPhoneResponseModel>(
            this.AllAsNoTracking()
                .Where(c => c.ContactDetails
                    .Any(cd => cd.ContactDetailId == (int)ContactDetailId.Phone && cd.Value == phoneNumber)
                )).FirstOrDefaultAsync();

    private async Task<int> CreateNewClientAsync(ClientRelatedClientsRequestModel request)
    {
        var personalSphereId = await sourceCategoriesAppService.GetIdByNameAsync(this.personalSphere);

        var model = new ClientRequestModel
        {
            PersonalData = new ClientPersonalDataRequestModel
            {
                FirstName = request.RelatedClientFirstName,
                LastName = request.RelatedClientLastName,
                MiddleName = request.RelatedClientMiddleName,
            },
            ContactDetails = new List<ClientContactDetailRequestModel>
            {
                new()
                {
                    ContactDetailId = (int)ContactDetailId.Phone,
                    Value = request.PhoneNumber,
                },
                new()
                {
                    ContactDetailId = (int)ContactDetailId.Email,
                    Value = request.Email,
                }
            },
            ClientsSourceCategories = new List<ClientSourceCategoryRequestModel>
            {
                new()
                {
                    SourceCategoryId = personalSphereId,
                }
            }
        };

        return await this.CreateAsync(model);
    }

    private async Task<int> CreateNewCompany(ClientRelatedCompaniesRequestModel request, int clientId, int employeeId)
    {
        var sourceCategoryId = await this.Data.ClientsSourceCategories
            .Where(x => x.ClientId == clientId && x.EmployeeId == employeeId)
            .Select(x => x.SourceCategoryId)
            .FirstOrDefaultAsync();

        if (sourceCategoryId <= 0)
        {
            throw new UserFriendlyException("Source category not found.");
        }

        return await this.CreateAsync(new ClientRequestModel
        {
            TypeId = (int)ClientTypeId.Legal,
            ClientsSourceCategories = new List<ClientSourceCategoryRequestModel>
            {
                new()
                {
                    SourceCategoryId = sourceCategoryId,
                    ClientId = clientId,
                    EmployeeId = employeeId
                }
            },

            LegalEntity = new ClientLegalEntityRequestModel
            {
                UnifiedIdentificationCode = request.UnifiedIdentificationCode,
                Name = request.Name
            }
        });
    }
}