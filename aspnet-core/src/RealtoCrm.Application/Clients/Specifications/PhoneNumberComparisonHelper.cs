namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;
using ContactDetails;
using Expressions;

public static class PhoneNumberComparisonHelper
{
    /// <summary>
    /// Creates an expression that checks if a contact detail value matches any of the common phone number formats.
    /// </summary>
    /// <param name="originalPhone">The original phone number to compare against</param>
    /// <param name="normalizedPhoneNumber">The normalized phone number (without country code)</param>
    /// <returns>An expression that can be used in LINQ queries to match phone numbers</returns>
    public static Expression<Func<ClientContactDetail, bool>> CreatePhoneMatchExpression(string originalPhone, string normalizedPhoneNumber)
    {
        return ccd =>
            ccd.Value == originalPhone ||
            ccd.Value == normalizedPhoneNumber ||
            ccd.Value == $"0{normalizedPhoneNumber}" ||
            ccd.Value == $"00359{normalizedPhoneNumber}" ||
            ccd.Value == $"+359{normalizedPhoneNumber}" ||
            ccd.Value == $"359{normalizedPhoneNumber}";
    }

    /// <summary>
    /// Creates a complete expression for finding clients by phone number including all common formats.
    /// </summary>
    /// <param name="phone">The phone number to search for</param>
    /// <returns>An expression that matches clients with the given phone number in any common format</returns>
    public static Expression<Func<Client, bool>> CreateClientPhoneMatchExpression(string phone)
    {
        var normalizedPhoneNumber = phone.TrimPhoneNumberCountryCode();
        var phoneMatchExpression = CreatePhoneMatchExpression(phone, normalizedPhoneNumber);

        return client => client.ContactDetails
            .Where(ccd => ccd.ContactDetail.Name == CosherConsts.ContactDetails.PhoneContactDetailName)
            .Any(phoneMatchExpression.Compile());
    }
}
