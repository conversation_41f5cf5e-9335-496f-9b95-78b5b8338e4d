namespace RealtoCrm.Clients.Specifications;

using System;
using System.Linq;
using System.Linq.Expressions;
using Expressions;

public class ClientByClientStatusSpecification(ClientStatus? clientStatus, ClientOwnership? clientOwnership)
    : Specification<Client>
{
    private const int ActiveOfferStatusId = 3;
    private const int ActiveSearchStatusId = 1;
    private const int PassedMeetingStatusId = 2;
    private const int PerformedViewing = 3;

    protected override bool Include => clientStatus != null && clientOwnership is ClientOwnership.My;

    public override Expression<Func<Client, bool>> ToExpression()
    {
        Expression<Func<Client, bool>> expression = clientStatus switch
        {
            ClientStatus.Contact => client => true,

            ClientStatus.Potential => IsPotentialClient(),
            ClientStatus.Meeting => IsClientWithMeetings(),
            ClientStatus.Contract => IsClientWithContract(),
            ClientStatus.Viewing => IsClientWithViewings(),
            ClientStatus.Deposit => IsClientWithDeposits(),
            _ => client => false
        };

        return expression;
    }

    private static Expression<Func<Client, bool>> IsPotentialClient()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            ExpressionsHelper.Not(HasPassedMeetings()),
            //TODO add contracts logic when implemented
            ExpressionsHelper.Not(HasViewingsForActiveSearches()),
            ExpressionsHelper.Not(HasDepositsForActiveOffers()),
            ExpressionsHelper.Not(HasDepositsForActiveSearches())
        );


    private static Expression<Func<Client, bool>> IsClientWithMeetings()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            HasPassedMeetings(),
            //TODO add contracts logic when implemented
            ExpressionsHelper.Not(HasViewingsForActiveSearches()),
            ExpressionsHelper.Not(HasDepositsForActiveOffers()),
            ExpressionsHelper.Not(HasDepositsForActiveSearches())
        );


    private static Expression<Func<Client, bool>> IsClientWithContract()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            HasPassedMeetings(),
            //TODO add contracts logic when implemented
            ExpressionsHelper.Not(HasViewingsForActiveSearches()),
            ExpressionsHelper.Not(HasDepositsForActiveOffers()),
            ExpressionsHelper.Not(HasDepositsForActiveSearches())
        );

    private static Expression<Func<Client, bool>> IsClientWithViewings()
        => ExpressionsHelper.And(
            HasActiveSearches(),
            HasPassedMeetings(),
            //TODO add contracts logic when implemented
                HasViewingsForActiveSearches(),
            ExpressionsHelper.Not(HasDepositsForActiveOffers()),
            ExpressionsHelper.Not(HasDepositsForActiveSearches())
        );

    private static Expression<Func<Client, bool>> IsClientWithDeposits()
        => ExpressionsHelper.And(
            HasActiveSearchesOrOffers(),
            HasPassedMeetings(),
            //TODO add contracts logic when implemented
                HasViewingsForActiveSearches(),
            ExpressionsHelper.Or(
                HasDepositsForActiveOffers(),
                HasDepositsForActiveSearches())
        );

    private static Expression<Func<Client, bool>> HasActiveOffers()
        => client => client.Offers.Any(o => o.OfferStatusId == ActiveOfferStatusId);

    private static Expression<Func<Client, bool>> HasActiveSearches()
        => client => client.Searches.Any(s => s.SearchStatusId == ActiveSearchStatusId);

    private static Expression<Func<Client, bool>> HasPassedMeetings()
        => client => client.Meetings.Any(m => m.MeetingStatusId == PassedMeetingStatusId);

    private static Expression<Func<Client, bool>> HasViewingsForActiveSearches()
        => client => client.Searches.Any(s =>
            s.SearchStatusId == ActiveSearchStatusId &&
            s.Viewings.Any(v => v.StatusId == PerformedViewing));

    private static Expression<Func<Client, bool>> HasDepositsForActiveOffers()
        => client => client.Offers.Any(o => o.OfferStatusId == ActiveOfferStatusId && o.Deposits.Any());

    private static Expression<Func<Client, bool>> HasDepositsForActiveSearches()
        => client => client.Searches.Any(s =>
            s.SearchStatusId == ActiveSearchStatusId && s.Deposits.Count != 0);

    private static Expression<Func<Client, bool>> HasActiveSearchesOrOffers()
    {
        return ExpressionsHelper.Or(HasActiveOffers(), HasActiveSearches());
    }
}