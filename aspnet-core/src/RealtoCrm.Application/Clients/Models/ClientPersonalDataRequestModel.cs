namespace RealtoCrm.Clients.Models;

using System;
using System.ComponentModel.DataAnnotations;
using Mapping;
using static RealtoCrm.ModelConstants.Common;
using ClientAnnotations = RealtoCrm.Clients.ModelConstants.Client;

public class ClientPersonalDataRequestModel : IMapTo<ClientPersonalData>
{
    public int? Id { get; init; }
    
    [Required]
    [MinLength(ClientAnnotations.MinNameLength)]
    [MaxLength(ClientAnnotations.MaxNameLength)]
    public string FirstName { get; init; } = default!;

    [MinLength(ClientAnnotations.MinNameLength)]
    [MaxLength(ClientAnnotations.MaxNameLength)]
    public string? MiddleName { get; init; }

    [Required]
    [MinLength(ClientAnnotations.MinNameLength)]
    [MaxLength(ClientAnnotations.MaxNameLength)]
    public string LastName { get; init; } = default!;

    public int? TitleId { get; init; }

    [MinLength(MinIdentificationNumberLength)]
    [MaxLength(MaxIdentificationNumberLength)]
    public string? IdentificationNumber { get; init; }

    public DateTime? BirthDate { get; init; }

    [MinLength(ClientAnnotations.MinDocumentNumberLength)]
    [MaxLength(ClientAnnotations.MaxDocumentNumberLength)]
    public string? DocumentNumber { get; init; }

    public DateTime? DocumentIssueDate { get; init; }

    [MinLength(ClientAnnotations.MinDocumentAuthorityLength)]
    [MaxLength(ClientAnnotations.MaxDocumentAuthorityLength)]
    public string? DocumentAuthority { get; init; }

    public int? GenderId { get; init; }

    public int? MaritalStatusId { get; init; }
}