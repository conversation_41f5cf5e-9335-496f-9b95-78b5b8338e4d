namespace RealtoCrm.Clients.Models;

using RealtoCrm.DataCrudModels;

public class ClientsPaginatedRequestModel : PaginatedRequestModel
{
    public string? SearchTerm { get; init; }
    
    public string? SearchPhoneOrEmail { get; init; }
    
    public string? UnifiedIdentificationCode { get; init; }
    
    public string? FirstName { get; init; }
    
    public string? LastName { get; init; }
    
    public string? CompanyName { get; init; }
    
    public string? CompanyEic { get; init; }
    
    public bool? IsIndividual { get; init; }
    
    public string[]? Source { get; init; }
    
    public string[]? SourceDetail { get; init; }
    
    public bool? HasSource { get; init; }
    
    public string? Phone { get; init; }
    
    public string? Email { get; init; }
    
    public string? CreationTime { get; init; }

    public string? Type { get; set; }

    public ClientStatus? ClientStatus { get; set; }
    
    public ClientOwnership? ClientOwnership { get; set; }
}