namespace RealtoCrm.Clients.Models;

using System.Linq;
using AutoMapper;
using ContactDetails.Models;
using Mapping;

public class ClientShortDetailsResponseModel : IMapFrom<Client>, IMapExplicitly
{
    public int Id { get; init; }
    
    public bool IsPersonalContact { get; init; }

    public string FirstName { get; init; } = string.Empty;

    public string LastName { get; init; } = string.Empty;

    public string Email { get; init; } = string.Empty;

    public string PhoneNumber { get; init; } = string.Empty;
    
    public string IdentificationNumber { get; init; } = string.Empty;
    
    public string UnifiedIdentificationCode { get; init; } = string.Empty;
    

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Client, ClientShortDetailsResponseModel>()
            .ForMember(m => m.FirstName, cfg => cfg
                .MapFrom(m => m.PersonalData != null
                    ? m.PersonalData.FirstName
                    : string.Empty))
            .ForMember(m => m.IsPersonalContact, cfg => cfg
                .MapFrom(m => m.Type.Id  == (int)ClientTypeId.Personal))
            .ForMember(m => m.UnifiedIdentificationCode, cfg => cfg
                .MapFrom(m => m.LegalEntity != null
                    ? m.LegalEntity.UnifiedIdentificationCode
                    : string.Empty))
            .ForMember(m => m.LastName, cfg => cfg
                .MapFrom(m => m.PersonalData != null
                    ? m.PersonalData.LastName
                    : string.Empty))
            .ForMember(m => m.IdentificationNumber, cfg => cfg
                .MapFrom(m => m.PersonalData != null
                    ? m.PersonalData.IdentificationNumber
                    : string.Empty))
            .ForMember(m => m.Email, cfg => cfg
                .MapFrom(m =>
                    m.ContactDetails.FirstOrDefault(cd => cd.ContactDetailId == (int)ContactDetailId.Email) != null
                        ? m.ContactDetails.First(cd => cd.ContactDetailId == (int)ContactDetailId.Email).Value
                        : string.Empty))
            .ForMember(m => m.PhoneNumber, cfg => cfg
                .MapFrom(m =>
                    m.ContactDetails.FirstOrDefault(cd => cd.ContactDetailId == (int)ContactDetailId.Phone) != null
                        ? m.ContactDetails.First(cd => cd.ContactDetailId == (int)ContactDetailId.Phone).Value
                        : string.Empty));
}