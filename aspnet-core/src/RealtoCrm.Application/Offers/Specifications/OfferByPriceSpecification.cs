namespace RealtoCrm.Offers.Specifications;

using System;
using System.Linq.Expressions;

public class OfferByPriceSpecification : Specification<Offer>
{
    private readonly int? priceFrom;
    private readonly int? priceTo;

    public OfferByPriceSpecification(int? priceFrom, int? priceTo)
    {
        this.priceFrom = priceFrom;
        this.priceTo = priceTo;
    }
    
    protected override bool Include => this.priceFrom is not null ||
                                       this.priceTo is not null ||
                                       this.priceFrom > 0 ||
                                       this.priceTo > 0;

    public override Expression<Func<Offer, bool>> ToExpression()
        => offer => priceFrom != null && priceTo != null
            ? offer.Price.Amount >= this.priceFrom && offer.Price.Amount <= this.priceTo &&
              this.priceTo > offer.Price.Amount
            : priceFrom != null && priceTo == null
                ? offer.Price.Amount >= this.priceFrom
                : offer.Price.Amount <= this.priceTo;
}