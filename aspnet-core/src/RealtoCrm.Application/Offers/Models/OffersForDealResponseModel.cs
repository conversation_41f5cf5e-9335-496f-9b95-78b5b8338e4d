namespace RealtoCrm.Offers.Models;

using System.Linq;
using Addresses.Models;
using AutoMapper;
using Mapping;

public class OffersForDealResponseModel : IMapFrom<Offer>, IMapExplicitly
{
    public int Id { get; init; }

    public int OperationTypeId { get; init; }

    public string OperationTypeName { get; init; } = default!;

    public string EstateTypeName { get; init; } = default!;

    public AddressResponseModel EstateAddress { get; init; } = default!;

    public int ClientId { get; init; }

    public string ClientFirstName { get; init; } = default!;

    public string ClientLastName { get; init; } = default!;

    public int EmployeeId { get; init; }

    public string EmployeeFirstName { get; init; } = default!;

    public string EmployeeLastName { get; init; } = default!;

    public string EmployeePhoneNumber { get; init; } = default!;

    public long? EmployeeUserId { get; init; }

    public string? ImageSource { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Offer, OffersForDealResponseModel>()
            .ForMember(m => m.ImageSource, cfg => cfg
                .MapFrom(m => m.OffersImages
                    .Select(oi => oi.Image.Source)
                    .FirstOrDefault()));
}