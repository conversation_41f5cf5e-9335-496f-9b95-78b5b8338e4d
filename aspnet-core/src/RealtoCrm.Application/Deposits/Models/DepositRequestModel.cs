namespace RealtoCrm.Deposits.Models;

using System;
using System.Collections.Generic;
using Mapping;
using Money;

public class DepositRequestModel : IMapTo<Deposit>
{
    public DateTime DateTime { get; init; }

    public DateTime SigningDate { get; init; }

    public DateTime SigningEndDate { get; init; }

    public MoneyRequestModel Amount { get; init; } = default!;

    public MoneyRequestModel OfferedPrice { get; init; } = default!;

    public int DepositStatusId { get; set; }

    public int MatchId { get; set; }

    public int SearchId { get; init; }

    public int SearchClientId { get; init; }

    public int SearchEmployeeId { get; init; }

    public int OfferId { get; init; }

    public int OfferClientId { get; init; }

    public int OfferEmployeeId { get; init; }

    public IEnumerable<DepositCommentRequestModel> DepositsComments { get; init; } =
        new List<DepositCommentRequestModel>();
}