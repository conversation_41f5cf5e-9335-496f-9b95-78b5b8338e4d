namespace RealtoCrm.Searches.Models;

using System;
using RealtoCrm.Employees.Models;
using RealtoCrm.SourceCategories.Models;
using RealtoCrm.ContactDetails.Models;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Comments.Models;
using Mapping;
using Money;
using static CosherConsts.Clients;

public class SearchResponseModel : IMapFrom<Search>, IMapExplicitly
{
    public int Id { get; init; }

    public string Type { get; init; } = default!;

    public bool? NoFirstFloor { get; init; }

    public bool? NoLastFloor { get; init; }

    public bool? HasBasement { get; init; }
    
    public double? CeilingHeightFrom { get; set; }

    public double? CeilingHeightTo { get; set; }

    public bool? HighCeiling { get; init; }

    public int? TerracesCount { get; init; }

    public int? BedroomsFrom { get; init; }

    public int? BedroomsTo { get; init; }

    public int? BathroomsFrom { get; init; }

    public int? BathroomsTo { get; init; }

    public double AreaFrom { get; init; }

    public double AreaTo { get; init; }

    public double? UsefulFloorAreaFrom { get; init; }

    public double? UsefulFloorAreaTo { get; init; }

    public double? KintAreaFrom { get; set; }

    public double? KintAreaTo { get; set; }

    public double? PlotAreaFrom { get; set; }

    public double? PlotAreaTo { get; set; }

    public double? DensityAreaFrom { get; set; }

    public double? DensityAreaTo { get; set; }

    public double? PossibleAreaFrom { get; set; }

    public double? PossibleAreaTo { get; set; }

    public double? AreaBuildUpFrom { get; set; }

    public double? AreaBuildUpTo { get; set; }

    public int? ConstructionPurposeId { get; init; }

    public string? ConstructionPurposeName { get; init; }

    public bool? WithCreditCenter { get; init; }

    public int? FloorFrom { get; set; }

    public int? FloorTo { get; set; }

    public int? BuildingYearFrom { get; set; }

    public int? BuildingYearTo { get; set; }

    public int? RoomsFrom { get; set; }

    public int? RoomsTo { get; set; }

    public int? SearchStatusId { get; init; }

    public string? SearchStatusName { get; init; }

    public string? ClientFirstName { get; init; }

    public string? ClientLastName { get; init; }
    
    public int? ArchiveReasonId { get; init; }

    public bool IsArchived => this.ArchiveReasonId is not null;
    
    public string? ArchiveReasonName { get; init; }
    
    public DateTime? ArchiveDate { get; init; }
    
    public ICollection<string> ClientPhoneNumbers { get; init; } = new List<string>();

    public  ICollection<string> ClientEmails { get; init; } = new List<string>();

    public MoneyResponseModel MoneyFrom { get; init; } = default!;

    public MoneyResponseModel MoneyTo { get; init; } = default!;

    public MoneyResponseModel? SquareMetrePriceFrom { get; init; }

    public MoneyResponseModel? SquareMetrePriceTo { get; init; }

    public SearchDetailResponseModel? SearchDetail { get; init; }

    public SourceCategoryResponseModel? SourceCategory { get; init; }

    public SourceDetailSearchResponseModel? SourceDetailSearch { get; init; }

    public int? EmployeeId { get; init; }

    public EmployeeForSearchResponseModel? Employee { get; init; }

    public int TenantId { get; init; }

    public bool HasViewings { get; init; }
    
    public int ClientId { get; init; }

    public ICollection<SearchCountryResponseModel> SearchesCountries { get; init; } =
        new List<SearchCountryResponseModel>();

    public ICollection<SearchProvinceResponseModel> SearchesProvinces { get; init; } =
        new List<SearchProvinceResponseModel>();

    public ICollection<SearchPopulatedPlaceResponseModel> SearchesPopulatedPlaces { get; init; } =
        new List<SearchPopulatedPlaceResponseModel>();

    public ICollection<SearchEstateTypeResponseModel> SearchesEstateTypes { get; init; } =
        new List<SearchEstateTypeResponseModel>();

    public ICollection<SearchDistrictResponseModel> SearchesDistricts { get; init; } =
        new List<SearchDistrictResponseModel>();

    public ICollection<SearchOfferResponseModel> SearchesOffers { get; init; } = new List<SearchOfferResponseModel>();

    public ICollection<SearchFinancingResponseModel> SearchesFinancing { get; init; } =
        new List<SearchFinancingResponseModel>();

    public ICollection<SearchDealMotiveResponseModel> SearchesDealMotives { get; init; } =
        new List<SearchDealMotiveResponseModel>();

    public ICollection<SearchRegulationResponseModel> SearchesRegulations { get; init; } =
        new List<SearchRegulationResponseModel>();

    public ICollection<SearchHouseTypeResponseModel> SearchesHouseTypes { get; init; } =
        new List<SearchHouseTypeResponseModel>();

    public ICollection<SearchHeatingResponseModel> SearchesHeating { get; init; } =
        new List<SearchHeatingResponseModel>();

    public ICollection<SearchConditionResponseModel> SearchesConditions { get; init; } =
        new List<SearchConditionResponseModel>();

    public ICollection<SearchFurnitureResponseModel> SearchesFurniture { get; init; } =
        new List<SearchFurnitureResponseModel>();

    public ICollection<SearchCompletionLevelResponseModel> SearchesCompletionLevels { get; init; } =
        new List<SearchCompletionLevelResponseModel>();

    public ICollection<SearchLifestyleResponseModel> SearchesLifestyles { get; init; } =
        new List<SearchLifestyleResponseModel>();

    public ICollection<SearchFacingDirectionResponseModel> SearchesFacingDirections { get; init; } =
        new List<SearchFacingDirectionResponseModel>();

    public ICollection<SearchEstateGroupResponseModel> SearchesEstateGroups { get; init; } =
        new List<SearchEstateGroupResponseModel>();

    public ICollection<SearchConstructionTypeResponseModel> SearchesConstructionTypes { get; init; } =
        new List<SearchConstructionTypeResponseModel>();

    public ICollection<SearchGarageResponseModel> SearchesGarages { get; init; } =
        new List<SearchGarageResponseModel>();

    public ICollection<CommentResponseModel> Comments { get; init; } = new List<CommentResponseModel>();

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Search, SearchResponseModel>()
            .ForMember(dest => dest.Comments, cfg => cfg
                .MapFrom(src => src.SearchesComments.Select(sc => sc.Comment)))
            .ForMember(dest => dest.HasViewings, cfg => cfg
                .MapFrom(src => src.Viewings.Count != 0))
            .ForMember(dest => dest.ClientFirstName, opt => opt
                .MapFrom(src => src.Client.TypeId == PersonalClientId && src.Client.PersonalData != null
                    ? src.Client.PersonalData.FirstName
                    : src.Client.LegalEntity != null
                        ? src.Client.LegalEntity.Name
                        : string.Empty))
            .ForMember(dest => dest.ClientLastName, opt => opt
                .MapFrom(src => src.Client.TypeId == PersonalClientId && src.Client.PersonalData != null
                    ? src.Client.PersonalData.LastName
                    : string.Empty))
            .ForMember(dest => dest.ClientPhoneNumbers, opt => opt
                .MapFrom(src => src.Client.ContactDetails
                    .Where(x => x.ContactDetailId == (int)ContactDetailId.Phone).Select(x => x.Value).ToList()))
            .ForMember(dest => dest.ClientEmails, opt => opt
                .MapFrom(src => src.Client.ContactDetails
                    .Where(x => x.ContactDetailId == (int)ContactDetailId.Email).Select(x => x.Value).ToList()));
}