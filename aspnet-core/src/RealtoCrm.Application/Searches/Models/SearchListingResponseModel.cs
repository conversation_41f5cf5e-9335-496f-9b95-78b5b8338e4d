namespace RealtoCrm.Searches.Models;

using System;
using System.Collections.Generic;
using System.Linq;
using AutoMapper;
using Comments.Models;
using ContactDetails.Models;
using Mapping;
using Money;
using Nomenclatures;
using static CosherConsts.ContractTypes;

public class SearchListingResponseModel : IMapFrom<Search>, IMapExplicitly
{
    private const int CommentsCountToDisplay = 3;
    public int Id { get; init; }

    public string Type { get; init; } = default!;

    public double AreaFrom { get; init; }

    public double AreaTo { get; init; }

    public MoneyResponseModel SquareMetrePriceFrom { get; init; } = default!;

    public MoneyResponseModel SquareMetrePriceTo { get; init; } = default!;

    public int ClientId { get; init; }

    public string? ClientName { get; init; }

    public string? ClientEmail { get; init; }

    public string? ClientPhoneNumber { get; init; }

    public int? EmployeeId { get; init; }

    public string? EmployeeName { get; init; }

    public string? EmployeePhoneNumber { get; init; }

    public string? EmployeeEmail { get; init; }

    public double UsefulFloorAreaFrom { get; init; }

    public double UsefulFloorAreaTo { get; init; }

    public string? CreationTime { get; init; }

    public MoneyResponseModel? MoneyFrom { get; init; }

    public MoneyResponseModel? MoneyTo { get; init; }

    public string? PopulatedPlaceName { get; init; }

    public List<string> Districts { get; init; } = default!;

    public string? EstateCategoryName { get; init; }

    public List<string> EstateTypes { get; init; } = default!;

    public int? ViewingsCount { get; init; }

    public DateTime? LastViewingDate { get; init; }

    public int? ArchiveReasonId { get; init; }

    public long? EmployeeUserId { get; init; }

    public string? SearchStatusName { get; init; }
    
    public string? ArchiveReasonName { get; init; }
    
    public DateTime? ArchiveDate { get; init; }
    
    public List<CommentResponseModel> Comments { get; set; } = new();
    
    public bool IsArchived => this.ArchiveReasonId is not null;

    public ArchiveType? ArchiveType { get; init; }

    public bool IsActiveWithoutContract { get; init; }

    public bool IsActiveWithCommissionContract { get; init; }

    public bool IsActiveWithExclusiveContract { get; init; }
    
    public bool IsDealSearch { get; init; }

    public void RegisterMappings(IProfileExpression mapper)
        => mapper
            .CreateMap<Search, SearchListingResponseModel>()
            .ForMember(slm => slm.EmployeeName, cfg => cfg
                .MapFrom(s =>
                    string.IsNullOrWhiteSpace(s.Employee.FirstName) && string.IsNullOrWhiteSpace(s.Employee.LastName)
                        ? null
                        : $"{s.Employee.FirstName} {s.Employee.LastName}"))
            .ForMember(slm => slm.EmployeeEmail, cfg => cfg
                .MapFrom(s => s.Employee != null ? s.Employee!.UserAccount.EmailAddress : null))
            .ForMember(slm => slm.ClientName, cfg => cfg
                .MapFrom(s => $"{s.Client.PersonalData!.FirstName} {s.Client.PersonalData.LastName}"))
            .ForMember(slm => slm.ClientEmail, cfg => cfg
                .MapFrom(s => s.Client.ContactDetails.Any(d => d.ContactDetailId == (int)ContactDetailId.Email)
                    ? s.Client.ContactDetails
                        .Where(d => d.ContactDetailId == (int)ContactDetailId.Email)
                        .Select(d => d.Value)
                        .FirstOrDefault()
                    : null))
            .ForMember(slm => slm.ClientPhoneNumber, cfg => cfg
                .MapFrom(s => s.Client.ContactDetails.Any(d => d.ContactDetailId == (int)ContactDetailId.Phone)
                    ? s.Client.ContactDetails
                        .Where(d => d.ContactDetailId == (int)ContactDetailId.Phone)
                        .Select(d => d.Value)
                        .FirstOrDefault()
                    : null))
            .ForMember(slm => slm.PopulatedPlaceName, cfg => cfg
                .MapFrom(s => s.SearchesPopulatedPlaces.Count > 0
                    ? s.SearchesPopulatedPlaces.First().PopulatedPlace.Name
                    : null))
            .ForMember(slm => slm.Districts, cfg => cfg
                .MapFrom(s => s.SearchesDistricts
                    .Select(sd => sd.District.Name)
                    .ToList()))
            .ForMember(slm => slm.EstateTypes, cfg => cfg
                .MapFrom(s => s.SearchesEstateTypes
                    .Select(x => x.EstateType.Name)
                    .ToList()))
            .ForMember(slm => slm.EstateCategoryName, cfg => cfg
                .MapFrom(s => s.SearchesEstateTypes.Count > 0
                    ? s.SearchesEstateTypes.FirstOrDefault()!.EstateType.Category.Name
                    : null))
            .ForMember(slm => slm.ViewingsCount, cfg => cfg
                .MapFrom(s => s.Viewings.Count))
            .ForMember(slm => slm.LastViewingDate, cfg => cfg
                .MapFrom(s => s.Viewings.Any()
                    ? s.Viewings.OrderByDescending(v => v.CreationTime).First().CreationTime
                    : (DateTime?)null))
            .ForMember(slm => slm.EmployeeUserId, cfg => cfg
                .MapFrom(s => s.Employee != null
                    ? s.Employee.UserAccount.UserId
                    : (long?)null))
            .ForMember(slm => slm.Comments, cfg => cfg
                .MapFrom(src => src.SearchesComments.Where(sc => sc.Comment.IsPrivate == false)
                    .Select(sc => sc.Comment)
                    .OrderByDescending(sc => sc.CreationTime)
                    .Take(CommentsCountToDisplay)))
            .ForMember(slm => slm.ArchiveType, cfg => cfg
                .MapFrom(s => s.ArchiveReason != null ? s.ArchiveReason.ArchiveType : (ArchiveType?)null))
            .ForMember(slm => slm.IsActiveWithoutContract, cfg => cfg
                .MapFrom(s => s.SearchStatusId == (int)SearchStatusId.Active && s.Contracts.Count == 0))
            .ForMember(slm => slm.IsActiveWithCommissionContract, cfg => cfg
                .MapFrom(s => s.SearchStatusId == (int)SearchStatusId.Active && s.Contracts.OrderByDescending(x => x.CreationTime).Any(c =>
                    c.ContractType != null && c.ContractType.Name == CommissionContractTypeName)))
            .ForMember(slm => slm.IsActiveWithExclusiveContract, cfg => cfg
                .MapFrom(s => s.SearchStatusId == (int)SearchStatusId.Active && s.Contracts.OrderByDescending(x => x.CreationTime).Any(c =>
                    c.ContractType != null && c.ContractType.Name == ExclusiveContractTypeName)))
            .ForMember(slm => slm.IsDealSearch, cfg => cfg
                .MapFrom(s => s.SearchStatusId == (int)SearchStatusId.Deal));

}