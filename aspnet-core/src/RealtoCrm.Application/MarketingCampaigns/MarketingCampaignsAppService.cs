using RealtoCrm.MarketingCampaigns.Specifications;

namespace RealtoCrm.MarketingCampaigns;

using Abp.EntityFrameworkCore;
using EntityFrameworkCore;
using Models;
using RealtoCrm.DataCrudModels;
using Expressions;
using SourceCategories;

public class MarketingCampaignsAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        MarketingCampaign,
        MarketingCampaignRequestModel,
        MarketingCampaignRequestModel,
        MarketingCampaignPaginatedRequestModel,
        MarketingCampaignResponseModel,
        MarketingCampaignResponseModel>(dbContextProvider, expressionsBuilder), IMarketingCampaignsAppService

{
    protected override Specification<MarketingCampaign> GetSpecification(MarketingCampaignPaginatedRequestModel request)
        => new MarketingCampaignByNameSpecification(request.SearchTerm);
}