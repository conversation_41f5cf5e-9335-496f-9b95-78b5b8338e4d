namespace RealtoCrm.Estates;

using System.Threading.Tasks;
using Abp.EntityFrameworkCore;
using Authorization;
using Common.Attributes;
using DataCrudModels;
using EntityFrameworkCore;
using Models;
using Expressions;
using Nomenclatures.OfferStatuses;
using Nomenclatures.OperationTypes;
using static CosherConsts.OfferStatuses;
using static CosherConsts.OperationTypes;

public class EstatesAppService(
    IOfferStatusesAppService offerStatusesAppService,
    IOperationTypesAppService operationTypesAppService,
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Estate,
        EstateCreateRequestModel,
        EstateUpdateRequestModel,
        PaginatedRequestModel,
        EstateDetailsResponseModel,
        EstateListingResponseModel>(dbContextProvider, expressionsBuilder), IEstatesAppService
{
    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(
                nameof(this.CreateAsync),
                AppPermissions.EstateCreate,
                AppPermissions.ProjectCreate,
                AppPermissions.LinkedProjectsCreate)
            .AddCustomAttribute(nameof(this.CreateAsync), new RequireTenantAttribute());

    public override async Task<int> CreateAsync(EstateCreateRequestModel request)
    {
        request.TenantId = this.GetTenantId();
        request.OfferStatusId = await offerStatusesAppService.GetIdByNameAsync(DraftOfferStatusName);
        request.OfferOperationTypeId = await operationTypesAppService.GetIdByNameAsync(SellingOperationTypeName);

        return await base.CreateAsync(request);
    }
}