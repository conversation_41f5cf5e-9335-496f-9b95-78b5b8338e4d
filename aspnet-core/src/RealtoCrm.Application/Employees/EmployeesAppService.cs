namespace RealtoCrm.Employees;

using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using Abp.Authorization;
using Abp.Authorization.Users;
using Abp.Domain.Entities;
using Abp.EntityFrameworkCore;
using Abp.UI;
using Authorization;
using Authorization.Roles;
using Authorization.Users;
using Authorization.Users.Profile.Dto;
using Common.Attributes;
using Common.Extensions;
using Companies;
using DataCrudModels;
using EntityFrameworkCore;
using Microsoft.EntityFrameworkCore;
using Models;
using Expressions;
using Specifications;

public class EmployeesAppService(
    IDbContextProvider<RealtoCrmDbContext> dbContextProvider,
    IUserAppService userAppService,
    IUserLinkAppService userLinkAppService,
    IRoleAppService roleAppService,
    ICompaniesAppService companiesAppService,
    IPermission<PERSON>hecker permissionChecker,
    IExpressionsBuilder expressionsBuilder)
    : DataCrudAppService<
        int,
        Employee,
        UserWithEmployeeCreateRequestModel,
        UserWithEmployeeUpdateRequestModel,
        EmployeesPaginatedRequestModel,
        EmployeeDetailsResponseModel,
        EmployeeListingResponseModel>(dbContextProvider, expressionsBuilder), IEmployeesAppService
{
    protected override bool IgnoreQueryFilters => true;
    private const int EmployeesCountToTake = 10;

    public async Task<int?> GetIdOrDefaultAsync(long userId)
        => await this.Find(userId, e => (int?)e.Id, shouldBeEmployee: false);

    public async Task<bool> IsEmployeeAsync(long id)
        => await this.AllAsNoTracking().AnyAsync(e => e.UserAccountId == id);

    protected override Dictionary<string, FilterExpression> CustomFilters
        => new()
        {
            [nameof(EmployeeListingResponseModel.EmailAddress)]
                = new FilterExpression(
                    typeof(UserAccount).GetProperty(nameof(UserAccount.EmailAddress))!,
                    Expression.PropertyOrField(
                        Expression.PropertyOrField(this.Parameter, nameof(Employee.UserAccount)),
                        nameof(UserAccount.EmailAddress))),
            [nameof(EmployeeListingResponseModel.TeamName)]
                = new FilterExpression(
                    typeof(Team).GetProperty(nameof(Team.Name))!,
                    Expression.PropertyOrField(
                        Expression.PropertyOrField(this.Parameter, nameof(Employee.Team)), nameof(Team.Name))),
            [nameof(EmployeeListingResponseModel.DivisionName)]
                = new FilterExpression(
                    typeof(Division).GetProperty(nameof(Division.Name))!,
                    Expression.PropertyOrField(
                        Expression.PropertyOrField(
                            Expression.PropertyOrField(this.Parameter, nameof(Employee.Office)),
                            nameof(Office.Division)),
                        nameof(Division.Name))),
            [nameof(EmployeeListingResponseModel.DepartmentName)]
                = new FilterExpression(
                    typeof(Department).GetProperty(nameof(Department.Name))!,
                    Expression.PropertyOrField(
                            Expression.PropertyOrField(this.Parameter, nameof(Employee.Department)),
                            nameof(Employee.Department.Name)))
        };

    protected override ActionAttributeConfiguration AttributeConfiguration
        => new ActionAttributeConfiguration()
            .ConfigureAuthorize(nameof(this.CreateAsync), AppPermissions.EmployeeCreate)
            .ConfigureAuthorize(nameof(this.UpdateAsync), AppPermissions.EmployeeUpdate)
            .ConfigureAuthorize(nameof(this.DeleteAsync), AppPermissions.EmployeeDelete)
            .ConfigureAuthorize(nameof(this.GetDetailsAsync), AppPermissions.EmployeeRead)
            .ConfigureAuthorize(nameof(this.GetIdOrDefaultAsync));

    public override async Task<EmployeeDetailsResponseModel?> GetDetailsAsync(int id)
    {
        var employee = await base.GetDetailsAsync(id);

        if (employee is null)
        {
            return null;
        }

        var managersUserRoles = await roleAppService.GetManagersByTenantAsync(employee.TenantId);
        var managersUserId = managersUserRoles.Select(r => r.UserId).ToList();

        // We need to fetch company, roles and managers in separate queries because we don't have navigation properties
        employee.CompanyName = await companiesAppService.GetCompanyNameByTenantAsync(employee.TenantId);
        employee.Roles = await roleAppService.GetAllByUserId(employee.UserId);
        employee.Managers = await this.GetEmployeeManagersAsync(id, managersUserId);
        employee.LinkedUsers = await userLinkAppService.GetLinkedUsersAsync(employee.UserId);

        return employee;
    }

    public override async Task<int> CreateAsync(UserWithEmployeeCreateRequestModel request)
    {
        var tenant = await companiesAppService.GetTenantByIdAsync(request.TenantId);

        var (_, employee) = await userAppService.CreateWithEmployeeForTenantAsync(
            tenant,
            request.User,
            request.Employee);

        return employee?.Id ?? 0;
    }

    [AbpAuthorize(AppPermissions.UserUpdate)]
    public override async Task<int> UpdateAsync(int id, UserWithEmployeeUpdateRequestModel request)
    {
        var employee = await this.GetAsync(id);

        if (employee is null)
        {
            throw new EntityNotFoundException($"Employee with id '{id}' is not found.");
        }

        this.ObjectMapper.Map(request.Employee, employee);

        await userAppService.UpdateUserAsync(request.TenantId, employee.UserAccount.UserId, request.User);

        await this.Data.SaveChangesAsync();

        return employee.Id;
    }

    public override async Task<bool> DeleteAsync(int id)
        => await userAppService.DeleteAsync(id);

    protected override async Task<Expression<Func<Employee, bool>>> BuildDataCrudFiltersExpression(
        IEnumerable<DataFilter> filters)
    {
        var filtersExpression = await base.BuildDataCrudFiltersExpression(filters);
        var customFilterExpressions = await this.BuildEmployeeCustomFilters(filters);

        if (customFilterExpressions is not null)
        {
            filtersExpression = filtersExpression.And(customFilterExpressions);
        }

        return filtersExpression;
    }

    private async Task<Expression<Func<Employee, bool>>?> BuildEmployeeCustomFilters(IEnumerable<DataFilter> filters)
    {
        var custom = filters
            .Where(f => f.Operator is not null
                        && !string.IsNullOrWhiteSpace(f.Value)
                        && f.NameFixed
                            is nameof(EmployeeListingResponseModel.Company)
                            or nameof(EmployeeListingResponseModel.IsActive)
                            or nameof(EmployeeListingResponseModel.IsEmailConfirmed))
            .ToList();

        if (!custom.Any())
        {
            return null;
        }

        var companyFilter = custom.FirstOrDefault(f => f.NameFixed == nameof(EmployeeListingResponseModel.Company));
        var isActiveFilter = custom.FirstOrDefault(f => f.NameFixed == nameof(EmployeeListingResponseModel.IsActive));
        var isEmailConfirmedFilter = custom
            .FirstOrDefault(f => f.NameFixed == nameof(EmployeeListingResponseModel.IsEmailConfirmed));

        var tenantsIds = new List<int?>();

        if (companyFilter is not null)
        {
            Expression<Func<Company, bool>> companyPredicate = companyFilter.Operator switch
            {
                DataFilterOperator.Equals => c => c.Name.ToLower() == companyFilter.Value!.ToLower(),
                DataFilterOperator.NotEquals => c => c.Name.ToLower() != companyFilter.Value!.ToLower(),
                DataFilterOperator.Contains => c => c.Name.ToLower().Contains(companyFilter.Value!.ToLower()),
                DataFilterOperator.NotContains => c => !c.Name.ToLower().Contains(companyFilter.Value!.ToLower()),
                DataFilterOperator.StartsWith => c => c.Name.ToLower().StartsWith(companyFilter.Value!.ToLower()),
                DataFilterOperator.EndsWith => c => c.Name.ToLower().EndsWith(companyFilter.Value!.ToLower()),
                _ => throw new InvalidOperationException(
                    $"Invalid operator '{companyFilter.Operator}' for company filter.")
            };

            tenantsIds = await this.Data
                .Companies
                .AsNoTracking()
                .Where(companyPredicate)
                .Select(c => (int?)c.TenantId)
                .ToListAsync();
        }

        var activeUserIds = isActiveFilter is null
            ? []
            : await this.Data
                .Users
                .AsNoTracking()
                .IgnoreQueryFilters()
                .Where(u => u.IsActive == bool.Parse(isActiveFilter.Value))
                .Select(u => u.Id)
                .ToListAsync();

        var emailConfirmedUserIds = isEmailConfirmedFilter is null
            ? []
            : await this.Data
                .Users
                .AsNoTracking()
                .IgnoreQueryFilters()
                .Where(u => u.IsEmailConfirmed == bool.Parse(isEmailConfirmedFilter.Value))
                .Select(u => u.Id)
                .ToListAsync();

        Expression<Func<Employee, bool>>? tenantFilterExpression = tenantsIds.Any()
            ? e => tenantsIds.Contains(e.UserAccount.TenantId)
            : null;

        Expression<Func<Employee, bool>>? isActiveFilterExpression = activeUserIds.Any()
            ? e => activeUserIds.Contains(e.UserAccount.UserId)
            : null;

        Expression<Func<Employee, bool>>? emailConfirmedFilterExpression = emailConfirmedUserIds.Any()
            ? e => emailConfirmedUserIds.Contains(e.UserAccount.UserId)
            : null;

        var filterExpressions = new List<Expression<Func<Employee, bool>>?>
            {
                tenantFilterExpression,
                isActiveFilterExpression,
                emailConfirmedFilterExpression
            }
            .Where(f => f is not null)
            .ToList();

        return !filterExpressions.Any()
            ? null
            : filterExpressions.Aggregate((left, right) => left!.And(right!));
    }

    protected override async Task<Specification<Employee>> GetSpecificationAsync(EmployeesPaginatedRequestModel request)
        => new EmployeeByEmailSpecification(request.Email)
            .And(new EmployeeByNameSpecification(request.Name))
            .And(new EmployeesOnlyActiveSpecification(request.OnlyActive))
            .And(new AccessToEmployeesSpecification(
                await permissionChecker.IsGrantedAsync(AppPermissions.UserRead),
                this.AbpSession.TenantId));

    public async Task<EmployeeByPhoneResponseModel> FindByPhoneAsync(string phoneNumber)
        => await this.ObjectMapper.ProjectTo<EmployeeByPhoneResponseModel>(
               this.All()
                   .Where(e => e.PhoneNumber == phoneNumber)
           ).FirstOrDefaultAsync()
           ?? throw new EntityNotFoundException($"Employee with phone number '{phoneNumber}' is not found.");

    public async Task<EmployeesClientTypesCount?> GetEmployeesClientsTypesCountAsync(int? employeeId)
        => await this.ObjectMapper
            .ProjectTo<EmployeesClientTypesCount>(this
                .AllAsNoTracking()
                .Where(x => x.Id == employeeId))
            .FirstOrDefaultAsync();

    public async Task<IEnumerable<EmployeeShortResponseModel>> GetByIdOrNameOrPhoneOrEmail(string? searchTerm)
        => await this.ObjectMapper.ProjectTo<EmployeeShortResponseModel>(
                this.AllAsNoTracking()
                    .Where(
                        new EmployeeByTenantSpecification(this.AbpSession.TenantId).And(
                            new EmployeeByNameSpecification(searchTerm).Or(
                                new EmployeeByPhoneSpecification(searchTerm).Or(
                                    new EmployeeByIdSpecification(searchTerm).Or(
                                        new EmployeeByEmailSpecification(searchTerm)))))
                    ))
            .Take(EmployeesCountToTake)
            .ToListAsync();

    public async Task<EmployeeShortResponseModel?> GetByUserId(long? userId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeShortResponseModel>(this.Data.Employees.Where(x => x.UserAccount.UserId == userId))
            .FirstOrDefaultAsync();

    public async Task<EmployeeForLoggedInUserResponseModel?> GetEmployeeForLoggedInUserAsync(long userId)
        => await this.ObjectMapper.ProjectTo<EmployeeForLoggedInUserResponseModel>(
            this.All()
                .Where(e => e.UserAccount.UserId == userId)
        ).FirstOrDefaultAsync();

    public async Task<int> UpdateDisplayNameForProfileAsync(long userId, CurrentUserProfileEditDto editModel)
    {
        var employee = await this.GetByUserIdAsync(userId);

        if (employee is null)
        {
            throw new EntityNotFoundException($"Employee associated with user with id '{userId}' is not found.");
        }

        if (employee.DisplayName != editModel.DisplayName)
        {
            var employeeUpdateModel = this.ObjectMapper.Map<EmployeeUpdateDisplayNameRequestModel>(editModel);
            this.ObjectMapper.Map(employeeUpdateModel, employee);

            await this.Data.SaveChangesAsync();
        }

        return employee.Id;
    }

    public async Task<EmployeeNameResponseModel?> GetNameByUserIdAsync(long userId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeNameResponseModel>(this
                .AllAsNoTracking()
                .Where(e => e.UserAccount.UserId == userId))
            .FirstOrDefaultAsync();

    public async Task<IEnumerable<EmployeeByManagerIdResponseModel>> GetEmployeesByManagerId(int employeeId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeByManagerIdResponseModel>(this
                .AllAsNoTracking()
                .Include(e => e.UserAccount)
                .Where(e => e.ManagerId == employeeId))
            .ToListAsync();
    
    public async Task<EmployeeDetailsResponseModel?> GetEmployeeDataById(int employeeId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeDetailsResponseModel>(this
                .AllAsNoTracking()
                .Include(e => e.UserAccount)
                .Where(e => e.Id == employeeId))
            .FirstOrDefaultAsync();

    protected override async Task<Employee?> GetAsync(int id)
        => await this
            .All()
            .IgnoreQueryFilters(this.IgnoreQueryFilters)
            .Include(e => e.UserAccount)
            .Where(e => e.Id == id)
            .FirstOrDefaultAsync();

    protected override async Task<IEnumerable<EmployeeListingResponseModel>> GetListingAsync(
        Specification<Employee> specification,
        Expression<Func<Employee, bool>> filtersExpression,
        IEnumerable<SortByExpressionDefinition<Employee>> sortByExpressions,
        int skip = 0,
        int take = int.MaxValue)
    {
        var employees = await base.GetListingAsync(
            specification,
            filtersExpression,
            sortByExpressions,
            skip,
            take);

        var userIds = employees
            .Select(e => e.UserId)
            .ToList();

        var tenantIds = employees
            .Select(e => e.TenantId)
            .ToList();

        var users = await userAppService.GetUsersActiveDetailsAsync(userIds);
        var companies = await companiesAppService.GetCompaniesByTenantsAsync(tenantIds);

        foreach (var employee in employees)
        {
            var user = users.FirstOrDefault(u => u.Id == employee.UserId);
            var company = companies.FirstOrDefault(c => c.TenantId == employee.TenantId);

            // TODO: Retrieve company name from mapping once company-to-employee logic is implemented
            employee.Company = company?.Name;
            employee.IsActive = user?.IsActive ?? false;
            employee.IsEmailConfirmed = user?.IsEmailConfirmed ?? false;
        }

        return employees;
    }

    private async Task<IEnumerable<EmployeeManagerResponseModel>> GetEmployeeManagersAsync(
        int id,
        IEnumerable<long> managersUserId)
        => await this.ObjectMapper
            .ProjectTo<EmployeeManagerResponseModel>(this
                .AllAsNoTracking()
                .Where(e => e.Id == id && e.Team != null)
                .SelectMany(e => e.Team!.Employees)
                .Where(e => managersUserId.Contains(e.UserAccount.UserId)))
            .ToListAsync();

    private async Task<T> Find<T>(
        long userId,
        Expression<Func<Employee, T>> selector,
        bool shouldBeEmployee = true)
    {
        var employee = await this
            .All()
            .Where(u => u.UserAccount.UserId == userId)
            .Select(selector)
            .FirstOrDefaultAsync();

        if (shouldBeEmployee && EqualityComparer<T>.Default.Equals(employee, default))
        {
            throw new UserFriendlyException("This user is not an employee.");
        }

        return employee!;
    }

    private async Task<Employee?> GetByUserIdAsync(long userId)
        => await this
            .All()
            .Where(e => e.UserAccount.UserId == userId)
            .FirstOrDefaultAsync();
}